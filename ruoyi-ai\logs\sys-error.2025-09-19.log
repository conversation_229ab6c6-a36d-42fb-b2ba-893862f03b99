2025-09-19 15:51:51 [main] ERROR o.s.b.d.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Parameter 0 of constructor in org.ruoyi.chat.config.AlipayConfiguration required a single bean, but 2 were found:
	- alipayProperties: defined in file [D:\daima\new\ruoyi-ai\ruoyi-modules\ruoyi-chat\target\classes\org\ruoyi\chat\config\AlipayProperties.class]
	- alipay.pay-org.ruoyi.chat.config.AlipayProperties: defined in unknown location

This may be due to missing parameter name information

Action:

Consider marking one of the beans as @Primary, updating the consumer to accept multiple beans, or using @Qualifier to identify the bean that should be consumed

Ensure that your compiler is configured to use the '-parameters' flag.
You may need to update both your build tool settings as well as your IDE.
(See https://github.com/spring-projects/spring-framework/wiki/Upgrading-to-Spring-Framework-6.x#parameter-name-retention)


