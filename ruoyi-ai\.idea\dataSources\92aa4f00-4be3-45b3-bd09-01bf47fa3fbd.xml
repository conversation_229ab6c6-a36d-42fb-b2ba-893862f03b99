<?xml version="1.0" encoding="UTF-8"?>
<dataSource name="ruoyi-ai@localhost">
  <database-model serializer="dbm" dbms="MYSQL" family-id="MYSQL" format-version="4.48">
    <root id="1">
      <DefaultCasing>lower/lower</DefaultCasing>
      <DefaultEngine>InnoDB</DefaultEngine>
      <DefaultTmpEngine>InnoDB</DefaultTmpEngine>
      <Grants>|root||root||ALTER|G
|root||root|localhost|ALTER|G
|root||root||ALTER ROUTINE|G
|root||root|localhost|ALTER ROUTINE|G
|root||root||APPLICATION_PASSWORD_ADMIN|G
|root||root|localhost|APPLICATION_PASSWORD_ADMIN|G
|root||mysql.infoschema|localhost|AUDIT_ABORT_EXEMPT|G
|root||mysql.session|localhost|AUDIT_ABORT_EXEMPT|G
|root||mysql.sys|localhost|AUDIT_ABORT_EXEMPT|G
|root||root||AUDIT_ABORT_EXEMPT|G
|root||root|localhost|AUDIT_ABORT_EXEMPT|G
|root||root||AUDIT_ADMIN|G
|root||root|localhost|AUDIT_ADMIN|G
|root||root||AUTHENTICATION_POLICY_ADMIN|G
|root||root|localhost|AUTHENTICATION_POLICY_ADMIN|G
|root||mysql.session|localhost|BACKUP_ADMIN|G
|root||root||BACKUP_ADMIN|G
|root||root|localhost|BACKUP_ADMIN|G
|root||root||BINLOG_ADMIN|G
|root||root|localhost|BINLOG_ADMIN|G
|root||root||BINLOG_ENCRYPTION_ADMIN|G
|root||root|localhost|BINLOG_ENCRYPTION_ADMIN|G
|root||mysql.session|localhost|CLONE_ADMIN|G
|root||root||CLONE_ADMIN|G
|root||root|localhost|CLONE_ADMIN|G
|root||mysql.session|localhost|CONNECTION_ADMIN|G
|root||root||CONNECTION_ADMIN|G
|root||root|localhost|CONNECTION_ADMIN|G
|root||root||CREATE|G
|root||root|localhost|CREATE|G
|root||root||CREATE ROLE|G
|root||root|localhost|CREATE ROLE|G
|root||root||CREATE ROUTINE|G
|root||root|localhost|CREATE ROUTINE|G
|root||root||CREATE TABLESPACE|G
|root||root|localhost|CREATE TABLESPACE|G
|root||root||CREATE TEMPORARY TABLES|G
|root||root|localhost|CREATE TEMPORARY TABLES|G
|root||root||CREATE USER|G
|root||root|localhost|CREATE USER|G
|root||root||CREATE VIEW|G
|root||root|localhost|CREATE VIEW|G
|root||root||DELETE|G
|root||root|localhost|DELETE|G
|root||root||DROP|G
|root||root|localhost|DROP|G
|root||root||DROP ROLE|G
|root||root|localhost|DROP ROLE|G
|root||root||ENCRYPTION_KEY_ADMIN|G
|root||root|localhost|ENCRYPTION_KEY_ADMIN|G
|root||root||EVENT|G
|root||root|localhost|EVENT|G
|root||root||EXECUTE|G
|root||root|localhost|EXECUTE|G
|root||root||FILE|G
|root||root|localhost|FILE|G
|root||mysql.infoschema|localhost|FIREWALL_EXEMPT|G
|root||mysql.session|localhost|FIREWALL_EXEMPT|G
|root||mysql.sys|localhost|FIREWALL_EXEMPT|G
|root||root||FIREWALL_EXEMPT|G
|root||root|localhost|FIREWALL_EXEMPT|G
|root||root||FLUSH_OPTIMIZER_COSTS|G
|root||root|localhost|FLUSH_OPTIMIZER_COSTS|G
|root||root||FLUSH_STATUS|G
|root||root|localhost|FLUSH_STATUS|G
|root||root||FLUSH_TABLES|G
|root||root|localhost|FLUSH_TABLES|G
|root||root||FLUSH_USER_RESOURCES|G
|root||root|localhost|FLUSH_USER_RESOURCES|G
|root||root||GROUP_REPLICATION_ADMIN|G
|root||root|localhost|GROUP_REPLICATION_ADMIN|G
|root||root||GROUP_REPLICATION_STREAM|G
|root||root|localhost|GROUP_REPLICATION_STREAM|G
|root||root||INDEX|G
|root||root|localhost|INDEX|G
|root||root||INNODB_REDO_LOG_ARCHIVE|G
|root||root|localhost|INNODB_REDO_LOG_ARCHIVE|G
|root||root||INNODB_REDO_LOG_ENABLE|G
|root||root|localhost|INNODB_REDO_LOG_ENABLE|G
|root||root||INSERT|G
|root||root|localhost|INSERT|G
|root||root||LOCK TABLES|G
|root||root|localhost|LOCK TABLES|G
|root||root||PASSWORDLESS_USER_ADMIN|G
|root||root|localhost|PASSWORDLESS_USER_ADMIN|G
|root||mysql.session|localhost|PERSIST_RO_VARIABLES_ADMIN|G
|root||root||PERSIST_RO_VARIABLES_ADMIN|G
|root||root|localhost|PERSIST_RO_VARIABLES_ADMIN|G
|root||root||PROCESS|G
|root||root|localhost|PROCESS|G
|root||root||REFERENCES|G
|root||root|localhost|REFERENCES|G
|root||root||RELOAD|G
|root||root|localhost|RELOAD|G
|root||root||REPLICATION CLIENT|G
|root||root|localhost|REPLICATION CLIENT|G
|root||root||REPLICATION SLAVE|G
|root||root|localhost|REPLICATION SLAVE|G
|root||root||REPLICATION_APPLIER|G
|root||root|localhost|REPLICATION_APPLIER|G
|root||root||REPLICATION_SLAVE_ADMIN|G
|root||root|localhost|REPLICATION_SLAVE_ADMIN|G
|root||root||RESOURCE_GROUP_ADMIN|G
|root||root|localhost|RESOURCE_GROUP_ADMIN|G
|root||root||RESOURCE_GROUP_USER|G
|root||root|localhost|RESOURCE_GROUP_USER|G
|root||root||ROLE_ADMIN|G
|root||root|localhost|ROLE_ADMIN|G
|root||mysql.infoschema|localhost|SELECT|G
|root||root||SELECT|G
|root||root|localhost|SELECT|G
|root||root||SENSITIVE_VARIABLES_OBSERVER|G
|root||root|localhost|SENSITIVE_VARIABLES_OBSERVER|G
|root||root||SERVICE_CONNECTION_ADMIN|G
|root||root|localhost|SERVICE_CONNECTION_ADMIN|G
|root||mysql.session|localhost|SESSION_VARIABLES_ADMIN|G
|root||root||SESSION_VARIABLES_ADMIN|G
|root||root|localhost|SESSION_VARIABLES_ADMIN|G
|root||root||SET_USER_ID|G
|root||root|localhost|SET_USER_ID|G
|root||root||SHOW DATABASES|G
|root||root|localhost|SHOW DATABASES|G
|root||root||SHOW VIEW|G
|root||root|localhost|SHOW VIEW|G
|root||root||SHOW_ROUTINE|G
|root||root|localhost|SHOW_ROUTINE|G
|root||mysql.session|localhost|SHUTDOWN|G
|root||root||SHUTDOWN|G
|root||root|localhost|SHUTDOWN|G
|root||mysql.session|localhost|SUPER|G
|root||root||SUPER|G
|root||root|localhost|SUPER|G
|root||mysql.infoschema|localhost|SYSTEM_USER|G
|root||mysql.session|localhost|SYSTEM_USER|G
|root||mysql.sys|localhost|SYSTEM_USER|G
|root||root||SYSTEM_USER|G
|root||root|localhost|SYSTEM_USER|G
|root||mysql.session|localhost|SYSTEM_VARIABLES_ADMIN|G
|root||root||SYSTEM_VARIABLES_ADMIN|G
|root||root|localhost|SYSTEM_VARIABLES_ADMIN|G
|root||root||TABLE_ENCRYPTION_ADMIN|G
|root||root|localhost|TABLE_ENCRYPTION_ADMIN|G
|root||root||TRIGGER|G
|root||root|localhost|TRIGGER|G
|root||root||UPDATE|G
|root||root|localhost|UPDATE|G
|root||root||XA_RECOVER_ADMIN|G
|root||root|localhost|XA_RECOVER_ADMIN|G
|root||root||grant option|G
|root||root|localhost|grant option|G
performance_schema|schema||mysql.session|localhost|SELECT|G
sys|schema||mysql.sys|localhost|TRIGGER|G</Grants>
      <ServerVersion>8.0.30</ServerVersion>
    </root>
    <collation id="2" parent="1" name="armscii8_general_ci">
      <Charset>armscii8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="3" parent="1" name="armscii8_bin">
      <Charset>armscii8</Charset>
    </collation>
    <collation id="4" parent="1" name="ascii_general_ci">
      <Charset>ascii</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="5" parent="1" name="ascii_bin">
      <Charset>ascii</Charset>
    </collation>
    <collation id="6" parent="1" name="big5_chinese_ci">
      <Charset>big5</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="7" parent="1" name="big5_bin">
      <Charset>big5</Charset>
    </collation>
    <collation id="8" parent="1" name="binary">
      <Charset>binary</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="9" parent="1" name="cp1250_general_ci">
      <Charset>cp1250</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="10" parent="1" name="cp1250_czech_cs">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="11" parent="1" name="cp1250_croatian_ci">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="12" parent="1" name="cp1250_bin">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="13" parent="1" name="cp1250_polish_ci">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="14" parent="1" name="cp1251_bulgarian_ci">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="15" parent="1" name="cp1251_ukrainian_ci">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="16" parent="1" name="cp1251_bin">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="17" parent="1" name="cp1251_general_ci">
      <Charset>cp1251</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="18" parent="1" name="cp1251_general_cs">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="19" parent="1" name="cp1256_general_ci">
      <Charset>cp1256</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="20" parent="1" name="cp1256_bin">
      <Charset>cp1256</Charset>
    </collation>
    <collation id="21" parent="1" name="cp1257_lithuanian_ci">
      <Charset>cp1257</Charset>
    </collation>
    <collation id="22" parent="1" name="cp1257_bin">
      <Charset>cp1257</Charset>
    </collation>
    <collation id="23" parent="1" name="cp1257_general_ci">
      <Charset>cp1257</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="24" parent="1" name="cp850_general_ci">
      <Charset>cp850</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="25" parent="1" name="cp850_bin">
      <Charset>cp850</Charset>
    </collation>
    <collation id="26" parent="1" name="cp852_general_ci">
      <Charset>cp852</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="27" parent="1" name="cp852_bin">
      <Charset>cp852</Charset>
    </collation>
    <collation id="28" parent="1" name="cp866_general_ci">
      <Charset>cp866</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="29" parent="1" name="cp866_bin">
      <Charset>cp866</Charset>
    </collation>
    <collation id="30" parent="1" name="cp932_japanese_ci">
      <Charset>cp932</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="31" parent="1" name="cp932_bin">
      <Charset>cp932</Charset>
    </collation>
    <collation id="32" parent="1" name="dec8_swedish_ci">
      <Charset>dec8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="33" parent="1" name="dec8_bin">
      <Charset>dec8</Charset>
    </collation>
    <collation id="34" parent="1" name="eucjpms_japanese_ci">
      <Charset>eucjpms</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="35" parent="1" name="eucjpms_bin">
      <Charset>eucjpms</Charset>
    </collation>
    <collation id="36" parent="1" name="euckr_korean_ci">
      <Charset>euckr</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="37" parent="1" name="euckr_bin">
      <Charset>euckr</Charset>
    </collation>
    <collation id="38" parent="1" name="gb18030_chinese_ci">
      <Charset>gb18030</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="39" parent="1" name="gb18030_bin">
      <Charset>gb18030</Charset>
    </collation>
    <collation id="40" parent="1" name="gb18030_unicode_520_ci">
      <Charset>gb18030</Charset>
    </collation>
    <collation id="41" parent="1" name="gb2312_chinese_ci">
      <Charset>gb2312</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="42" parent="1" name="gb2312_bin">
      <Charset>gb2312</Charset>
    </collation>
    <collation id="43" parent="1" name="gbk_chinese_ci">
      <Charset>gbk</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="44" parent="1" name="gbk_bin">
      <Charset>gbk</Charset>
    </collation>
    <collation id="45" parent="1" name="geostd8_general_ci">
      <Charset>geostd8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="46" parent="1" name="geostd8_bin">
      <Charset>geostd8</Charset>
    </collation>
    <collation id="47" parent="1" name="greek_general_ci">
      <Charset>greek</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="48" parent="1" name="greek_bin">
      <Charset>greek</Charset>
    </collation>
    <collation id="49" parent="1" name="hebrew_general_ci">
      <Charset>hebrew</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="50" parent="1" name="hebrew_bin">
      <Charset>hebrew</Charset>
    </collation>
    <collation id="51" parent="1" name="hp8_english_ci">
      <Charset>hp8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="52" parent="1" name="hp8_bin">
      <Charset>hp8</Charset>
    </collation>
    <collation id="53" parent="1" name="keybcs2_general_ci">
      <Charset>keybcs2</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="54" parent="1" name="keybcs2_bin">
      <Charset>keybcs2</Charset>
    </collation>
    <collation id="55" parent="1" name="koi8r_general_ci">
      <Charset>koi8r</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="56" parent="1" name="koi8r_bin">
      <Charset>koi8r</Charset>
    </collation>
    <collation id="57" parent="1" name="koi8u_general_ci">
      <Charset>koi8u</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="58" parent="1" name="koi8u_bin">
      <Charset>koi8u</Charset>
    </collation>
    <collation id="59" parent="1" name="latin1_german1_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="60" parent="1" name="latin1_swedish_ci">
      <Charset>latin1</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="61" parent="1" name="latin1_danish_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="62" parent="1" name="latin1_german2_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="63" parent="1" name="latin1_bin">
      <Charset>latin1</Charset>
    </collation>
    <collation id="64" parent="1" name="latin1_general_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="65" parent="1" name="latin1_general_cs">
      <Charset>latin1</Charset>
    </collation>
    <collation id="66" parent="1" name="latin1_spanish_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="67" parent="1" name="latin2_czech_cs">
      <Charset>latin2</Charset>
    </collation>
    <collation id="68" parent="1" name="latin2_general_ci">
      <Charset>latin2</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="69" parent="1" name="latin2_hungarian_ci">
      <Charset>latin2</Charset>
    </collation>
    <collation id="70" parent="1" name="latin2_croatian_ci">
      <Charset>latin2</Charset>
    </collation>
    <collation id="71" parent="1" name="latin2_bin">
      <Charset>latin2</Charset>
    </collation>
    <collation id="72" parent="1" name="latin5_turkish_ci">
      <Charset>latin5</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="73" parent="1" name="latin5_bin">
      <Charset>latin5</Charset>
    </collation>
    <collation id="74" parent="1" name="latin7_estonian_cs">
      <Charset>latin7</Charset>
    </collation>
    <collation id="75" parent="1" name="latin7_general_ci">
      <Charset>latin7</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="76" parent="1" name="latin7_general_cs">
      <Charset>latin7</Charset>
    </collation>
    <collation id="77" parent="1" name="latin7_bin">
      <Charset>latin7</Charset>
    </collation>
    <collation id="78" parent="1" name="macce_general_ci">
      <Charset>macce</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="79" parent="1" name="macce_bin">
      <Charset>macce</Charset>
    </collation>
    <collation id="80" parent="1" name="macroman_general_ci">
      <Charset>macroman</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="81" parent="1" name="macroman_bin">
      <Charset>macroman</Charset>
    </collation>
    <collation id="82" parent="1" name="sjis_japanese_ci">
      <Charset>sjis</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="83" parent="1" name="sjis_bin">
      <Charset>sjis</Charset>
    </collation>
    <collation id="84" parent="1" name="swe7_swedish_ci">
      <Charset>swe7</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="85" parent="1" name="swe7_bin">
      <Charset>swe7</Charset>
    </collation>
    <collation id="86" parent="1" name="tis620_thai_ci">
      <Charset>tis620</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="87" parent="1" name="tis620_bin">
      <Charset>tis620</Charset>
    </collation>
    <collation id="88" parent="1" name="ucs2_general_ci">
      <Charset>ucs2</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="89" parent="1" name="ucs2_bin">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="90" parent="1" name="ucs2_unicode_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="91" parent="1" name="ucs2_icelandic_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="92" parent="1" name="ucs2_latvian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="93" parent="1" name="ucs2_romanian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="94" parent="1" name="ucs2_slovenian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="95" parent="1" name="ucs2_polish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="96" parent="1" name="ucs2_estonian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="97" parent="1" name="ucs2_spanish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="98" parent="1" name="ucs2_swedish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="99" parent="1" name="ucs2_turkish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="100" parent="1" name="ucs2_czech_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="101" parent="1" name="ucs2_danish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="102" parent="1" name="ucs2_lithuanian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="103" parent="1" name="ucs2_slovak_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="104" parent="1" name="ucs2_spanish2_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="105" parent="1" name="ucs2_roman_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="106" parent="1" name="ucs2_persian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="107" parent="1" name="ucs2_esperanto_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="108" parent="1" name="ucs2_hungarian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="109" parent="1" name="ucs2_sinhala_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="110" parent="1" name="ucs2_german2_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="111" parent="1" name="ucs2_croatian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="112" parent="1" name="ucs2_unicode_520_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="113" parent="1" name="ucs2_vietnamese_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="114" parent="1" name="ucs2_general_mysql500_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="115" parent="1" name="ujis_japanese_ci">
      <Charset>ujis</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="116" parent="1" name="ujis_bin">
      <Charset>ujis</Charset>
    </collation>
    <collation id="117" parent="1" name="utf16_general_ci">
      <Charset>utf16</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="118" parent="1" name="utf16_bin">
      <Charset>utf16</Charset>
    </collation>
    <collation id="119" parent="1" name="utf16_unicode_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="120" parent="1" name="utf16_icelandic_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="121" parent="1" name="utf16_latvian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="122" parent="1" name="utf16_romanian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="123" parent="1" name="utf16_slovenian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="124" parent="1" name="utf16_polish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="125" parent="1" name="utf16_estonian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="126" parent="1" name="utf16_spanish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="127" parent="1" name="utf16_swedish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="128" parent="1" name="utf16_turkish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="129" parent="1" name="utf16_czech_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="130" parent="1" name="utf16_danish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="131" parent="1" name="utf16_lithuanian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="132" parent="1" name="utf16_slovak_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="133" parent="1" name="utf16_spanish2_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="134" parent="1" name="utf16_roman_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="135" parent="1" name="utf16_persian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="136" parent="1" name="utf16_esperanto_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="137" parent="1" name="utf16_hungarian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="138" parent="1" name="utf16_sinhala_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="139" parent="1" name="utf16_german2_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="140" parent="1" name="utf16_croatian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="141" parent="1" name="utf16_unicode_520_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="142" parent="1" name="utf16_vietnamese_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="143" parent="1" name="utf16le_general_ci">
      <Charset>utf16le</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="144" parent="1" name="utf16le_bin">
      <Charset>utf16le</Charset>
    </collation>
    <collation id="145" parent="1" name="utf32_general_ci">
      <Charset>utf32</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="146" parent="1" name="utf32_bin">
      <Charset>utf32</Charset>
    </collation>
    <collation id="147" parent="1" name="utf32_unicode_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="148" parent="1" name="utf32_icelandic_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="149" parent="1" name="utf32_latvian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="150" parent="1" name="utf32_romanian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="151" parent="1" name="utf32_slovenian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="152" parent="1" name="utf32_polish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="153" parent="1" name="utf32_estonian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="154" parent="1" name="utf32_spanish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="155" parent="1" name="utf32_swedish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="156" parent="1" name="utf32_turkish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="157" parent="1" name="utf32_czech_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="158" parent="1" name="utf32_danish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="159" parent="1" name="utf32_lithuanian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="160" parent="1" name="utf32_slovak_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="161" parent="1" name="utf32_spanish2_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="162" parent="1" name="utf32_roman_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="163" parent="1" name="utf32_persian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="164" parent="1" name="utf32_esperanto_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="165" parent="1" name="utf32_hungarian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="166" parent="1" name="utf32_sinhala_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="167" parent="1" name="utf32_german2_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="168" parent="1" name="utf32_croatian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="169" parent="1" name="utf32_unicode_520_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="170" parent="1" name="utf32_vietnamese_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="171" parent="1" name="utf8mb3_general_ci">
      <Charset>utf8mb3</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="172" parent="1" name="utf8mb3_tolower_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="173" parent="1" name="utf8mb3_bin">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="174" parent="1" name="utf8mb3_unicode_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="175" parent="1" name="utf8mb3_icelandic_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="176" parent="1" name="utf8mb3_latvian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="177" parent="1" name="utf8mb3_romanian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="178" parent="1" name="utf8mb3_slovenian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="179" parent="1" name="utf8mb3_polish_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="180" parent="1" name="utf8mb3_estonian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="181" parent="1" name="utf8mb3_spanish_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="182" parent="1" name="utf8mb3_swedish_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="183" parent="1" name="utf8mb3_turkish_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="184" parent="1" name="utf8mb3_czech_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="185" parent="1" name="utf8mb3_danish_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="186" parent="1" name="utf8mb3_lithuanian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="187" parent="1" name="utf8mb3_slovak_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="188" parent="1" name="utf8mb3_spanish2_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="189" parent="1" name="utf8mb3_roman_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="190" parent="1" name="utf8mb3_persian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="191" parent="1" name="utf8mb3_esperanto_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="192" parent="1" name="utf8mb3_hungarian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="193" parent="1" name="utf8mb3_sinhala_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="194" parent="1" name="utf8mb3_german2_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="195" parent="1" name="utf8mb3_croatian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="196" parent="1" name="utf8mb3_unicode_520_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="197" parent="1" name="utf8mb3_vietnamese_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="198" parent="1" name="utf8mb3_general_mysql500_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="199" parent="1" name="utf8mb4_general_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="200" parent="1" name="utf8mb4_bin">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="201" parent="1" name="utf8mb4_unicode_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="202" parent="1" name="utf8mb4_icelandic_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="203" parent="1" name="utf8mb4_latvian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="204" parent="1" name="utf8mb4_romanian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="205" parent="1" name="utf8mb4_slovenian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="206" parent="1" name="utf8mb4_polish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="207" parent="1" name="utf8mb4_estonian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="208" parent="1" name="utf8mb4_spanish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="209" parent="1" name="utf8mb4_swedish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="210" parent="1" name="utf8mb4_turkish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="211" parent="1" name="utf8mb4_czech_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="212" parent="1" name="utf8mb4_danish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="213" parent="1" name="utf8mb4_lithuanian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="214" parent="1" name="utf8mb4_slovak_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="215" parent="1" name="utf8mb4_spanish2_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="216" parent="1" name="utf8mb4_roman_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="217" parent="1" name="utf8mb4_persian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="218" parent="1" name="utf8mb4_esperanto_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="219" parent="1" name="utf8mb4_hungarian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="220" parent="1" name="utf8mb4_sinhala_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="221" parent="1" name="utf8mb4_german2_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="222" parent="1" name="utf8mb4_croatian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="223" parent="1" name="utf8mb4_unicode_520_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="224" parent="1" name="utf8mb4_vietnamese_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="225" parent="1" name="utf8mb4_0900_ai_ci">
      <Charset>utf8mb4</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="226" parent="1" name="utf8mb4_de_pb_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="227" parent="1" name="utf8mb4_is_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="228" parent="1" name="utf8mb4_lv_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="229" parent="1" name="utf8mb4_ro_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="230" parent="1" name="utf8mb4_sl_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="231" parent="1" name="utf8mb4_pl_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="232" parent="1" name="utf8mb4_et_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="233" parent="1" name="utf8mb4_es_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="234" parent="1" name="utf8mb4_sv_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="235" parent="1" name="utf8mb4_tr_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="236" parent="1" name="utf8mb4_cs_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="237" parent="1" name="utf8mb4_da_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="238" parent="1" name="utf8mb4_lt_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="239" parent="1" name="utf8mb4_sk_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="240" parent="1" name="utf8mb4_es_trad_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="241" parent="1" name="utf8mb4_la_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="242" parent="1" name="utf8mb4_eo_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="243" parent="1" name="utf8mb4_hu_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="244" parent="1" name="utf8mb4_hr_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="245" parent="1" name="utf8mb4_vi_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="246" parent="1" name="utf8mb4_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="247" parent="1" name="utf8mb4_de_pb_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="248" parent="1" name="utf8mb4_is_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="249" parent="1" name="utf8mb4_lv_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="250" parent="1" name="utf8mb4_ro_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="251" parent="1" name="utf8mb4_sl_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="252" parent="1" name="utf8mb4_pl_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="253" parent="1" name="utf8mb4_et_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="254" parent="1" name="utf8mb4_es_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="255" parent="1" name="utf8mb4_sv_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="256" parent="1" name="utf8mb4_tr_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="257" parent="1" name="utf8mb4_cs_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="258" parent="1" name="utf8mb4_da_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="259" parent="1" name="utf8mb4_lt_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="260" parent="1" name="utf8mb4_sk_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="261" parent="1" name="utf8mb4_es_trad_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="262" parent="1" name="utf8mb4_la_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="263" parent="1" name="utf8mb4_eo_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="264" parent="1" name="utf8mb4_hu_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="265" parent="1" name="utf8mb4_hr_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="266" parent="1" name="utf8mb4_vi_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="267" parent="1" name="utf8mb4_ja_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="268" parent="1" name="utf8mb4_ja_0900_as_cs_ks">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="269" parent="1" name="utf8mb4_0900_as_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="270" parent="1" name="utf8mb4_ru_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="271" parent="1" name="utf8mb4_ru_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="272" parent="1" name="utf8mb4_zh_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="273" parent="1" name="utf8mb4_0900_bin">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="274" parent="1" name="utf8mb4_nb_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="275" parent="1" name="utf8mb4_nb_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="276" parent="1" name="utf8mb4_nn_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="277" parent="1" name="utf8mb4_nn_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="278" parent="1" name="utf8mb4_sr_latn_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="279" parent="1" name="utf8mb4_sr_latn_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="280" parent="1" name="utf8mb4_bs_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="281" parent="1" name="utf8mb4_bs_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="282" parent="1" name="utf8mb4_bg_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="283" parent="1" name="utf8mb4_bg_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="284" parent="1" name="utf8mb4_gl_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="285" parent="1" name="utf8mb4_gl_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="286" parent="1" name="utf8mb4_mn_cyrl_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="287" parent="1" name="utf8mb4_mn_cyrl_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <schema id="288" parent="1" name="big_event">
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </schema>
    <schema id="289" parent="1" name="chatroom">
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </schema>
    <schema id="290" parent="1" name="dkd">
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </schema>
    <schema id="291" parent="1" name="exam">
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </schema>
    <schema id="292" parent="1" name="information_schema">
      <CollationName>utf8mb3_general_ci</CollationName>
    </schema>
    <schema id="293" parent="1" name="mall_pms">
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </schema>
    <schema id="294" parent="1" name="mall_ums">
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </schema>
    <schema id="295" parent="1" name="mall_user">
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </schema>
    <schema id="296" parent="1" name="mysql">
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </schema>
    <schema id="297" parent="1" name="nacos">
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </schema>
    <schema id="298" parent="1" name="newschool">
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </schema>
    <schema id="299" parent="1" name="performance_schema">
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </schema>
    <schema id="300" parent="1" name="ruanjian">
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </schema>
    <schema id="301" parent="1" name="ruoyi-ai">
      <Current>1</Current>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </schema>
    <schema id="302" parent="1" name="ry-vue">
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </schema>
    <schema id="303" parent="1" name="stument">
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </schema>
    <schema id="304" parent="1" name="sys">
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </schema>
    <schema id="305" parent="1" name="test">
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </schema>
    <schema id="306" parent="1" name="xiaomengfangwu">
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </schema>
    <user id="307" parent="1" name="root">
      <Plugin>caching_sha2_password</Plugin>
    </user>
    <user id="308" parent="1" name="mysql.infoschema">
      <CanLogin>0</CanLogin>
      <Host>localhost</Host>
      <Plugin>caching_sha2_password</Plugin>
    </user>
    <user id="309" parent="1" name="mysql.session">
      <CanLogin>0</CanLogin>
      <Host>localhost</Host>
      <Plugin>caching_sha2_password</Plugin>
    </user>
    <user id="310" parent="1" name="mysql.sys">
      <CanLogin>0</CanLogin>
      <Host>localhost</Host>
      <Plugin>caching_sha2_password</Plugin>
    </user>
    <user id="311" parent="1" name="root">
      <Host>localhost</Host>
      <Plugin>caching_sha2_password</Plugin>
    </user>
    <table id="312" parent="301" name="attention">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="313" parent="301" name="character_setting">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="314" parent="301" name="chat_config">
      <Comment>配置信息表</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="315" parent="301" name="chat_message">
      <Comment>聊天消息表</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="316" parent="301" name="chat_model">
      <Comment>聊天模型</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="317" parent="301" name="chat_pay_order">
      <Comment>支付订单表</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="318" parent="301" name="chat_session">
      <Comment>会话管理</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="319" parent="301" name="chat_usage_token">
      <Comment>用户token使用详情</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="320" parent="301" name="comment">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="321" parent="301" name="comment_goodbad">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="322" parent="301" name="gen_table">
      <Comment>代码生成业务表</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="323" parent="301" name="gen_table_column">
      <Comment>代码生成业务表字段</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="324" parent="301" name="knowledge_attach">
      <Comment>知识库附件</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="325" parent="301" name="knowledge_fragment">
      <Comment>知识片段</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="326" parent="301" name="knowledge_info">
      <Comment>知识库</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="327" parent="301" name="live2d">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="328" parent="301" name="live2dcontroller">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="329" parent="301" name="mynoval">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="330" parent="301" name="news">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="331" parent="301" name="news_comment">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="332" parent="301" name="noval">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="333" parent="301" name="noval_details">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="334" parent="301" name="noval_directory">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="335" parent="301" name="noval_exegesis">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="336" parent="301" name="novel_outline">
      <Comment>小说大纲表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="337" parent="301" name="sys_config">
      <Comment>参数配置表</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="338" parent="301" name="sys_dept">
      <Comment>部门表</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="339" parent="301" name="sys_dict_data">
      <Comment>字典数据表</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="340" parent="301" name="sys_dict_type">
      <Comment>字典类型表</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="341" parent="301" name="sys_file_info">
      <Comment>文件记录表</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb3_general_ci</CollationName>
    </table>
    <table id="342" parent="301" name="sys_logininfor">
      <Comment>系统访问记录</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="343" parent="301" name="sys_menu">
      <Comment>菜单权限表</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="344" parent="301" name="sys_notice">
      <Comment>通知公告表</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="345" parent="301" name="sys_notice_state">
      <Comment>用户阅读状态表</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="346" parent="301" name="sys_oper_log">
      <Comment>操作日志记录</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="347" parent="301" name="sys_oss">
      <Comment>OSS对象存储表</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="348" parent="301" name="sys_oss_config">
      <Comment>对象存储配置表</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="349" parent="301" name="sys_post">
      <Comment>岗位信息表</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="350" parent="301" name="sys_role">
      <Comment>角色信息表</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="351" parent="301" name="sys_role_dept">
      <Comment>角色和部门关联表</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="352" parent="301" name="sys_role_menu">
      <Comment>角色和菜单关联表</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="353" parent="301" name="sys_tenant">
      <Comment>租户表</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="354" parent="301" name="sys_tenant_package">
      <Comment>租户套餐表</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="355" parent="301" name="sys_user">
      <Comment>用户信息表</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="356" parent="301" name="sys_user_post">
      <Comment>用户与岗位关联表</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="357" parent="301" name="sys_user_role">
      <Comment>用户和角色关联表</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="358" parent="301" name="today">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <column id="359" parent="312" name="id">
      <DasType>varchar(50)|0s</DasType>
      <Position>1</Position>
    </column>
    <column id="360" parent="312" name="other_id">
      <DasType>varchar(50)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="361" parent="312" name="create_dept">
      <DasType>varchar(100)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="362" parent="312" name="create_by">
      <DasType>varchar(100)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="363" parent="312" name="create_time">
      <DasType>varchar(100)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="364" parent="312" name="update_by">
      <DasType>varchar(100)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="365" parent="312" name="update_time">
      <DasType>varchar(100)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="366" parent="313" name="id">
      <AutoIncrement>3</AutoIncrement>
      <Comment>角色设定ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="367" parent="313" name="book_id">
      <Comment>书籍ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="368" parent="313" name="book_name">
      <Comment>书籍名称</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="369" parent="313" name="character_name">
      <Comment>角色名称</Comment>
      <DasType>varchar(100)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="370" parent="313" name="character_description">
      <Comment>角色描述/设定内容</Comment>
      <DasType>text|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="371" parent="313" name="ai_analysis">
      <Comment>AI分析结果</Comment>
      <DasType>longtext|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="372" parent="313" name="character_type">
      <Comment>角色类型</Comment>
      <DasType>varchar(50)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="373" parent="313" name="importance">
      <Comment>角色重要性（1-5星级）</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>3</DefaultExpression>
      <Position>8</Position>
    </column>
    <column id="374" parent="313" name="del_flag">
      <Comment>删除标志（0代表存在 2代表删除）</Comment>
      <DasType>char(1)|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>9</Position>
    </column>
    <column id="375" parent="313" name="create_by">
      <Comment>鍒涘缓鑰</Comment>
      <DasType>varchar(64)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>10</Position>
    </column>
    <column id="376" parent="313" name="create_time">
      <Comment>鍒涘缓鏃堕棿</Comment>
      <DasType>datetime|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="377" parent="313" name="update_by">
      <Comment>鏇存柊鑰</Comment>
      <DasType>varchar(64)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>12</Position>
    </column>
    <column id="378" parent="313" name="update_time">
      <Comment>鏇存柊鏃堕棿</Comment>
      <DasType>datetime|0s</DasType>
      <Position>13</Position>
    </column>
    <column id="379" parent="313" name="remark">
      <Comment>澶囨敞</Comment>
      <DasType>varchar(500)|0s</DasType>
      <Position>14</Position>
    </column>
    <column id="380" parent="313" name="create_dept">
      <Comment>创建部门</Comment>
      <DasType>bigint|0s</DasType>
      <Position>15</Position>
    </column>
    <index id="381" parent="313" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="382" parent="313" name="idx_book_id">
      <ColNames>book_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="383" parent="313" name="idx_character_name">
      <ColNames>character_name</ColNames>
      <Type>btree</Type>
    </index>
    <key id="384" parent="313" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="385" parent="314" name="id">
      <AutoIncrement>1904862904897019906</AutoIncrement>
      <Comment>主键</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="386" parent="314" name="category">
      <Comment>配置类型</Comment>
      <DasType>varchar(255)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="387" parent="314" name="config_name">
      <Comment>配置名称</Comment>
      <DasType>varchar(255)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="388" parent="314" name="config_value">
      <Comment>配置值</Comment>
      <DasType>text|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="389" parent="314" name="config_dict">
      <Comment>说明</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="390" parent="314" name="create_dept">
      <Comment>创建部门</Comment>
      <DasType>bigint|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="391" parent="314" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="392" parent="314" name="create_by">
      <Comment>创建者</Comment>
      <DasType>varchar(64)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>8</Position>
    </column>
    <column id="393" parent="314" name="update_by">
      <Comment>更新者</Comment>
      <DasType>varchar(64)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>9</Position>
    </column>
    <column id="394" parent="314" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="395" parent="314" name="remark">
      <Comment>备注</Comment>
      <DasType>varchar(500)|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="396" parent="314" name="version">
      <Comment>版本</Comment>
      <DasType>int|0s</DasType>
      <Position>12</Position>
    </column>
    <column id="397" parent="314" name="del_flag">
      <Comment>删除标志（0代表存在 1代表删除）</Comment>
      <DasType>char(1)|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>13</Position>
    </column>
    <column id="398" parent="314" name="update_ip">
      <Comment>更新IP</Comment>
      <DasType>varchar(128)|0s</DasType>
      <Position>14</Position>
    </column>
    <column id="399" parent="314" name="tenant_id">
      <Comment>租户Id</Comment>
      <DasType>bigint|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>15</Position>
    </column>
    <index id="400" parent="314" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="401" parent="314" name="unique_category_key">
      <ColNames>category
config_name</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="402" parent="314" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="403" parent="314" name="unique_category_key">
      <UnderlyingIndexName>unique_category_key</UnderlyingIndexName>
    </key>
    <column id="404" parent="315" name="id">
      <Comment>主键</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="405" parent="315" name="session_id">
      <Comment>会话id</Comment>
      <DasType>bigint|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="406" parent="315" name="user_id">
      <Comment>用户id</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="407" parent="315" name="content">
      <Comment>消息内容</Comment>
      <DasType>longtext|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="408" parent="315" name="role">
      <Comment>对话角色</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="409" parent="315" name="deduct_cost">
      <Comment>扣除金额</Comment>
      <DasType>double(20,2 digit)|0s</DasType>
      <DefaultExpression>0.00</DefaultExpression>
      <Position>6</Position>
    </column>
    <column id="410" parent="315" name="total_tokens">
      <Comment>累计 Tokens</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>7</Position>
    </column>
    <column id="411" parent="315" name="model_name">
      <Comment>模型名称</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="412" parent="315" name="create_dept">
      <Comment>创建部门</Comment>
      <DasType>bigint|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="413" parent="315" name="create_by">
      <Comment>创建者</Comment>
      <DasType>bigint|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="414" parent="315" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="415" parent="315" name="update_by">
      <Comment>更新者</Comment>
      <DasType>bigint|0s</DasType>
      <Position>12</Position>
    </column>
    <column id="416" parent="315" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>13</Position>
    </column>
    <column id="417" parent="315" name="remark">
      <Comment>备注</Comment>
      <DasType>varchar(500)|0s</DasType>
      <Position>14</Position>
    </column>
    <index id="418" parent="315" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="419" parent="315" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="420" parent="316" name="id">
      <Comment>主键</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="421" parent="316" name="tenant_id">
      <Comment>租户编号</Comment>
      <DasType>varchar(20)|0s</DasType>
      <DefaultExpression>&apos;000000&apos;</DefaultExpression>
      <Position>2</Position>
    </column>
    <column id="422" parent="316" name="category">
      <Comment>模型分类</Comment>
      <DasType>varchar(20)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="423" parent="316" name="model_name">
      <Comment>模型名称</Comment>
      <DasType>varchar(50)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="424" parent="316" name="model_describe">
      <Comment>模型描述</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="425" parent="316" name="model_price">
      <Comment>模型价格</Comment>
      <DasType>double|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="426" parent="316" name="model_type">
      <Comment>计费类型</Comment>
      <DasType>char(1)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="427" parent="316" name="model_show">
      <Comment>是否显示</Comment>
      <DasType>char(1)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="428" parent="316" name="system_prompt">
      <Comment>系统提示词</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="429" parent="316" name="api_host">
      <Comment>请求地址</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="430" parent="316" name="api_key">
      <Comment>密钥</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="431" parent="316" name="api_url">
      <Comment>请求后缀</Comment>
      <DasType>varchar(50)|0s</DasType>
      <Position>12</Position>
    </column>
    <column id="432" parent="316" name="create_dept">
      <Comment>创建部门</Comment>
      <DasType>bigint|0s</DasType>
      <Position>13</Position>
    </column>
    <column id="433" parent="316" name="create_by">
      <Comment>创建者</Comment>
      <DasType>bigint|0s</DasType>
      <Position>14</Position>
    </column>
    <column id="434" parent="316" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>15</Position>
    </column>
    <column id="435" parent="316" name="update_by">
      <Comment>更新者</Comment>
      <DasType>bigint|0s</DasType>
      <Position>16</Position>
    </column>
    <column id="436" parent="316" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>17</Position>
    </column>
    <column id="437" parent="316" name="remark">
      <Comment>备注</Comment>
      <DasType>varchar(500)|0s</DasType>
      <Position>18</Position>
    </column>
    <index id="438" parent="316" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="439" parent="316" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="440" parent="317" name="id">
      <AutoIncrement>1</AutoIncrement>
      <Comment>主键</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="441" parent="317" name="order_no">
      <Comment>订单编号</Comment>
      <DasType>varchar(100)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="442" parent="317" name="order_name">
      <Comment>订单名称</Comment>
      <DasType>varchar(100)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="443" parent="317" name="amount">
      <Comment>金额</Comment>
      <DasType>decimal(10,2 digit)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="444" parent="317" name="payment_status">
      <Comment>支付状态</Comment>
      <DasType>char(1)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="445" parent="317" name="payment_method">
      <Comment>支付方式</Comment>
      <DasType>char(10)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="446" parent="317" name="user_id">
      <Comment>用户ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="447" parent="317" name="create_by">
      <Comment>创建者</Comment>
      <DasType>bigint|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="448" parent="317" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="449" parent="317" name="update_by">
      <Comment>更新者</Comment>
      <DasType>bigint|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="450" parent="317" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="451" parent="317" name="remark">
      <Comment>备注</Comment>
      <DasType>varchar(500)|0s</DasType>
      <Position>12</Position>
    </column>
    <column id="452" parent="317" name="create_dept">
      <Comment>创建部门</Comment>
      <DasType>bigint|0s</DasType>
      <Position>13</Position>
    </column>
    <index id="453" parent="317" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="454" parent="317" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="455" parent="318" name="id">
      <Comment>主键</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="456" parent="318" name="user_id">
      <Comment>用户id</Comment>
      <DasType>bigint|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="457" parent="318" name="session_title">
      <Comment>会话标题</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="458" parent="318" name="session_content">
      <Comment>会话内容</Comment>
      <DasType>text|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="459" parent="318" name="create_dept">
      <Comment>部门</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="460" parent="318" name="create_by">
      <Comment>创建者</Comment>
      <DasType>bigint|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="461" parent="318" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="462" parent="318" name="update_by">
      <Comment>更新者</Comment>
      <DasType>bigint|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="463" parent="318" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="464" parent="318" name="remark">
      <Comment>备注</Comment>
      <DasType>varchar(500)|0s</DasType>
      <Position>10</Position>
    </column>
    <index id="465" parent="318" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="466" parent="318" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="467" parent="319" name="id">
      <Comment>主键</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="468" parent="319" name="user_id">
      <Comment>用户</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="469" parent="319" name="token">
      <Comment>待结算token</Comment>
      <DasType>int|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="470" parent="319" name="model_name">
      <Comment>模型名称</Comment>
      <DasType>varchar(64)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="471" parent="319" name="total_token">
      <Comment>累计使用token</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>5</Position>
    </column>
    <index id="472" parent="319" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="473" parent="319" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="474" parent="320" name="commernt_id">
      <DasType>bigint|0s</DasType>
      <Position>1</Position>
    </column>
    <column id="475" parent="320" name="my_id">
      <DasType>bigint|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="476" parent="320" name="commernt_type">
      <DasType>int|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="477" parent="320" name="is_response">
      <DasType>int|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="478" parent="320" name="response_id">
      <DasType>bigint|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="479" parent="320" name="content">
      <DasType>varchar(200)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="480" parent="320" name="create_dept">
      <DasType>varchar(200)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="481" parent="320" name="create_by">
      <DasType>varchar(200)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="482" parent="320" name="create_time">
      <DasType>varchar(200)|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="483" parent="320" name="update_by">
      <DasType>varchar(200)|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="484" parent="320" name="update_time">
      <DasType>varchar(200)|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="485" parent="320" name="look">
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>12</Position>
    </column>
    <column id="486" parent="320" name="icon1">
      <DasType>varchar(200)|0s</DasType>
      <Position>13</Position>
    </column>
    <column id="487" parent="320" name="icon2">
      <DasType>varchar(200)|0s</DasType>
      <Position>14</Position>
    </column>
    <column id="488" parent="320" name="icon3">
      <DasType>varchar(200)|0s</DasType>
      <Position>15</Position>
    </column>
    <column id="489" parent="320" name="icon4">
      <DasType>varchar(200)|0s</DasType>
      <Position>16</Position>
    </column>
    <column id="490" parent="320" name="icon5">
      <DasType>varchar(200)|0s</DasType>
      <Position>17</Position>
    </column>
    <column id="491" parent="320" name="is_fine">
      <DasType>varchar(5)|0s</DasType>
      <Position>18</Position>
    </column>
    <column id="492" parent="320" name="fatherid">
      <DasType>int|0s</DasType>
      <Position>19</Position>
    </column>
    <column id="493" parent="321" name="commernt_id">
      <DasType>varchar(100)|0s</DasType>
      <Position>1</Position>
    </column>
    <column id="494" parent="321" name="who_id">
      <DasType>varchar(100)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="495" parent="321" name="good_bad">
      <DasType>int|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="496" parent="321" name="create_dept">
      <DasType>varchar(100)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="497" parent="321" name="create_by">
      <DasType>varchar(100)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="498" parent="321" name="create_time">
      <DasType>varchar(100)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="499" parent="321" name="update_by">
      <DasType>varchar(100)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="500" parent="321" name="update_time">
      <DasType>varchar(100)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="501" parent="322" name="table_id">
      <Comment>编号</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="502" parent="322" name="table_name">
      <Comment>表名称</Comment>
      <DasType>varchar(200)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>2</Position>
    </column>
    <column id="503" parent="322" name="table_comment">
      <Comment>表描述</Comment>
      <DasType>varchar(500)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>3</Position>
    </column>
    <column id="504" parent="322" name="sub_table_name">
      <Comment>关联子表的表名</Comment>
      <DasType>varchar(64)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="505" parent="322" name="sub_table_fk_name">
      <Comment>子表关联的外键名</Comment>
      <DasType>varchar(64)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="506" parent="322" name="class_name">
      <Comment>实体类名称</Comment>
      <DasType>varchar(100)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>6</Position>
    </column>
    <column id="507" parent="322" name="tpl_category">
      <Comment>使用的模板（crud单表操作 tree树表操作）</Comment>
      <DasType>varchar(200)|0s</DasType>
      <DefaultExpression>&apos;crud&apos;</DefaultExpression>
      <Position>7</Position>
    </column>
    <column id="508" parent="322" name="package_name">
      <Comment>生成包路径</Comment>
      <DasType>varchar(100)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="509" parent="322" name="module_name">
      <Comment>生成模块名</Comment>
      <DasType>varchar(30)|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="510" parent="322" name="business_name">
      <Comment>生成业务名</Comment>
      <DasType>varchar(30)|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="511" parent="322" name="function_name">
      <Comment>生成功能名</Comment>
      <DasType>varchar(50)|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="512" parent="322" name="function_author">
      <Comment>生成功能作者</Comment>
      <DasType>varchar(50)|0s</DasType>
      <Position>12</Position>
    </column>
    <column id="513" parent="322" name="gen_type">
      <Comment>生成代码方式（0zip压缩包 1自定义路径）</Comment>
      <DasType>char(1)|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>13</Position>
    </column>
    <column id="514" parent="322" name="gen_path">
      <Comment>生成路径（不填默认项目路径）</Comment>
      <DasType>varchar(200)|0s</DasType>
      <DefaultExpression>&apos;/&apos;</DefaultExpression>
      <Position>14</Position>
    </column>
    <column id="515" parent="322" name="options">
      <Comment>其它生成选项</Comment>
      <DasType>varchar(1000)|0s</DasType>
      <Position>15</Position>
    </column>
    <column id="516" parent="322" name="create_dept">
      <Comment>创建部门</Comment>
      <DasType>bigint|0s</DasType>
      <Position>16</Position>
    </column>
    <column id="517" parent="322" name="create_by">
      <Comment>创建者</Comment>
      <DasType>bigint|0s</DasType>
      <Position>17</Position>
    </column>
    <column id="518" parent="322" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>18</Position>
    </column>
    <column id="519" parent="322" name="update_by">
      <Comment>更新者</Comment>
      <DasType>bigint|0s</DasType>
      <Position>19</Position>
    </column>
    <column id="520" parent="322" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>20</Position>
    </column>
    <column id="521" parent="322" name="remark">
      <Comment>备注</Comment>
      <DasType>varchar(500)|0s</DasType>
      <Position>21</Position>
    </column>
    <index id="522" parent="322" name="PRIMARY">
      <ColNames>table_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="523" parent="322" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="524" parent="323" name="column_id">
      <Comment>编号</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="525" parent="323" name="table_id">
      <Comment>归属表编号</Comment>
      <DasType>bigint|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="526" parent="323" name="column_name">
      <Comment>列名称</Comment>
      <DasType>varchar(200)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="527" parent="323" name="column_comment">
      <Comment>列描述</Comment>
      <DasType>varchar(500)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="528" parent="323" name="column_type">
      <Comment>列类型</Comment>
      <DasType>varchar(100)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="529" parent="323" name="java_type">
      <Comment>JAVA类型</Comment>
      <DasType>varchar(500)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="530" parent="323" name="java_field">
      <Comment>JAVA字段名</Comment>
      <DasType>varchar(200)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="531" parent="323" name="is_pk">
      <Comment>是否主键（1是）</Comment>
      <DasType>char(1)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="532" parent="323" name="is_increment">
      <Comment>是否自增（1是）</Comment>
      <DasType>char(1)|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="533" parent="323" name="is_required">
      <Comment>是否必填（1是）</Comment>
      <DasType>char(1)|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="534" parent="323" name="is_insert">
      <Comment>是否为插入字段（1是）</Comment>
      <DasType>char(1)|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="535" parent="323" name="is_edit">
      <Comment>是否编辑字段（1是）</Comment>
      <DasType>char(1)|0s</DasType>
      <Position>12</Position>
    </column>
    <column id="536" parent="323" name="is_list">
      <Comment>是否列表字段（1是）</Comment>
      <DasType>char(1)|0s</DasType>
      <Position>13</Position>
    </column>
    <column id="537" parent="323" name="is_query">
      <Comment>是否查询字段（1是）</Comment>
      <DasType>char(1)|0s</DasType>
      <Position>14</Position>
    </column>
    <column id="538" parent="323" name="query_type">
      <Comment>查询方式（等于、不等于、大于、小于、范围）</Comment>
      <DasType>varchar(200)|0s</DasType>
      <DefaultExpression>&apos;EQ&apos;</DefaultExpression>
      <Position>15</Position>
    </column>
    <column id="539" parent="323" name="html_type">
      <Comment>显示类型（文本框、文本域、下拉框、复选框、单选框、日期控件）</Comment>
      <DasType>varchar(200)|0s</DasType>
      <Position>16</Position>
    </column>
    <column id="540" parent="323" name="dict_type">
      <Comment>字典类型</Comment>
      <DasType>varchar(200)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>17</Position>
    </column>
    <column id="541" parent="323" name="sort">
      <Comment>排序</Comment>
      <DasType>int|0s</DasType>
      <Position>18</Position>
    </column>
    <column id="542" parent="323" name="create_dept">
      <Comment>创建部门</Comment>
      <DasType>bigint|0s</DasType>
      <Position>19</Position>
    </column>
    <column id="543" parent="323" name="create_by">
      <Comment>创建者</Comment>
      <DasType>bigint|0s</DasType>
      <Position>20</Position>
    </column>
    <column id="544" parent="323" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>21</Position>
    </column>
    <column id="545" parent="323" name="update_by">
      <Comment>更新者</Comment>
      <DasType>bigint|0s</DasType>
      <Position>22</Position>
    </column>
    <column id="546" parent="323" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>23</Position>
    </column>
    <index id="547" parent="323" name="PRIMARY">
      <ColNames>column_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="548" parent="323" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="549" parent="324" name="id">
      <AutoIncrement>1926124407095468035</AutoIncrement>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="550" parent="324" name="kid">
      <Comment>知识库ID</Comment>
      <DasType>varchar(50)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="551" parent="324" name="doc_id">
      <Comment>文档ID</Comment>
      <DasType>varchar(50)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="552" parent="324" name="doc_name">
      <Comment>文档名称</Comment>
      <DasType>varchar(500)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="553" parent="324" name="doc_type">
      <Comment>文档类型</Comment>
      <DasType>varchar(50)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="554" parent="324" name="oss_id">
      <Comment>对象存储ID</Comment>
      <DasType>int|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="555" parent="324" name="pic_status">
      <Comment>拆解图片状态10未开始，20进行中，30已完成</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>10</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="556" parent="324" name="pic_anys_status">
      <Comment>分析图片状态10未开始，20进行中，30已完成</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>10</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
    </column>
    <column id="557" parent="324" name="vector_status">
      <Comment>写入向量数据库状态10未开始，20进行中，30已完成</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>10</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>9</Position>
    </column>
    <column id="558" parent="324" name="content">
      <Comment>文档内容</Comment>
      <DasType>longtext|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="559" parent="324" name="create_dept">
      <Comment>部门</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="560" parent="324" name="create_by">
      <Comment>创建人</Comment>
      <DasType>varchar(50)|0s</DasType>
      <Position>12</Position>
    </column>
    <column id="561" parent="324" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>13</Position>
    </column>
    <column id="562" parent="324" name="update_by">
      <Comment>更新者</Comment>
      <DasType>bigint|0s</DasType>
      <Position>14</Position>
    </column>
    <column id="563" parent="324" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>15</Position>
    </column>
    <column id="564" parent="324" name="remark">
      <Comment>备注</Comment>
      <DasType>varchar(500)|0s</DasType>
      <Position>16</Position>
    </column>
    <index id="565" parent="324" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="566" parent="324" name="idx_kname">
      <ColNames>kid
doc_name</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="567" parent="324" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="568" parent="324" name="idx_kname">
      <UnderlyingIndexName>idx_kname</UnderlyingIndexName>
    </key>
    <column id="569" parent="325" name="id">
      <AutoIncrement>1926124406994804743</AutoIncrement>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="570" parent="325" name="kid">
      <Comment>知识库ID</Comment>
      <DasType>varchar(50)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="571" parent="325" name="doc_id">
      <Comment>文档ID</Comment>
      <DasType>varchar(50)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="572" parent="325" name="fid">
      <Comment>知识片段ID</Comment>
      <DasType>varchar(50)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="573" parent="325" name="idx">
      <Comment>片段索引下标</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="574" parent="325" name="content">
      <Comment>文档内容</Comment>
      <DasType>text|0s</DasType>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="575" parent="325" name="create_dept">
      <Comment>部门</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="576" parent="325" name="create_by">
      <Comment>创建人</Comment>
      <DasType>varchar(50)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="577" parent="325" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="578" parent="325" name="update_by">
      <Comment>更新者</Comment>
      <DasType>bigint|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="579" parent="325" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="580" parent="325" name="remark">
      <Comment>备注</Comment>
      <DasType>varchar(500)|0s</DasType>
      <Position>12</Position>
    </column>
    <index id="581" parent="325" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="582" parent="325" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="583" parent="326" name="id">
      <AutoIncrement>1926124373012553730</AutoIncrement>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="584" parent="326" name="kid">
      <Comment>知识库ID</Comment>
      <DasType>varchar(50)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="585" parent="326" name="uid">
      <Comment>用户ID</Comment>
      <DasType>bigint|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="586" parent="326" name="kname">
      <Comment>知识库名称</Comment>
      <DasType>varchar(50)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="587" parent="326" name="share">
      <Comment>是否公开知识库（0 否 1是）</Comment>
      <DasType>tinyint|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="588" parent="326" name="description">
      <Comment>描述</Comment>
      <DasType>varchar(1000)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="589" parent="326" name="knowledge_separator">
      <Comment>知识分隔符</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="590" parent="326" name="question_separator">
      <Comment>提问分隔符</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="591" parent="326" name="overlap_char">
      <Comment>重叠字符数</Comment>
      <DasType>int|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="592" parent="326" name="retrieve_limit">
      <Comment>知识库中检索的条数</Comment>
      <DasType>int|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="593" parent="326" name="text_block_size">
      <Comment>文本块大小</Comment>
      <DasType>int|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="594" parent="326" name="vector_model_name">
      <Comment>向量库</Comment>
      <DasType>varchar(50)|0s</DasType>
      <Position>12</Position>
    </column>
    <column id="595" parent="326" name="embedding_model_name">
      <Comment>向量模型</Comment>
      <DasType>varchar(50)|0s</DasType>
      <Position>13</Position>
    </column>
    <column id="596" parent="326" name="system_prompt">
      <Comment>系统提示词</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>14</Position>
    </column>
    <column id="597" parent="326" name="create_dept">
      <Comment>部门</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>15</Position>
    </column>
    <column id="598" parent="326" name="create_by">
      <Comment>创建人</Comment>
      <DasType>varchar(50)|0s</DasType>
      <Position>16</Position>
    </column>
    <column id="599" parent="326" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>17</Position>
    </column>
    <column id="600" parent="326" name="update_by">
      <Comment>更新者</Comment>
      <DasType>bigint|0s</DasType>
      <Position>18</Position>
    </column>
    <column id="601" parent="326" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>19</Position>
    </column>
    <column id="602" parent="326" name="remark">
      <Comment>备注</Comment>
      <DasType>varchar(500)|0s</DasType>
      <Position>20</Position>
    </column>
    <index id="603" parent="326" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="604" parent="326" name="idx_kid">
      <ColNames>kid</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="605" parent="326" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="606" parent="326" name="idx_kid">
      <UnderlyingIndexName>idx_kid</UnderlyingIndexName>
    </key>
    <column id="607" parent="327" name="id">
      <DasType>bigint|0s</DasType>
      <Position>1</Position>
    </column>
    <column id="608" parent="327" name="original_name">
      <DasType>varchar(200)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="609" parent="327" name="name">
      <DasType>varchar(100)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="610" parent="327" name="type">
      <DasType>int|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="611" parent="327" name="create_dept">
      <DasType>varchar(100)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="612" parent="327" name="create_by">
      <DasType>varchar(100)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="613" parent="327" name="create_time">
      <DasType>varchar(100)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="614" parent="327" name="update_by">
      <DasType>varchar(100)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="615" parent="327" name="update_time">
      <DasType>varchar(100)|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="616" parent="328" name="id">
      <DasType>bigint|0s</DasType>
      <Position>1</Position>
    </column>
    <column id="617" parent="328" name="userid">
      <DasType>bigint|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="618" parent="328" name="name">
      <DasType>varchar(100)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="619" parent="328" name="create_dept">
      <DasType>varchar(100)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="620" parent="328" name="create_by">
      <DasType>varchar(100)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="621" parent="328" name="create_time">
      <DasType>varchar(100)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="622" parent="328" name="update_by">
      <DasType>varchar(100)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="623" parent="328" name="update_time">
      <DasType>varchar(100)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="624" parent="329" name="id">
      <DasType>bigint|0s</DasType>
      <Position>1</Position>
    </column>
    <column id="625" parent="329" name="userid">
      <DasType>bigint|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="626" parent="329" name="cosid">
      <DasType>bigint|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="627" parent="329" name="create_dept">
      <DasType>varchar(100)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="628" parent="329" name="create_by">
      <DasType>varchar(100)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="629" parent="329" name="create_time">
      <DasType>varchar(100)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="630" parent="329" name="update_by">
      <DasType>varchar(100)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="631" parent="329" name="update_time">
      <DasType>varchar(100)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="632" parent="330" name="id">
      <DasType>bigint|0s</DasType>
      <Position>1</Position>
    </column>
    <column id="633" parent="330" name="my_id">
      <DasType>bigint|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="634" parent="330" name="other_id">
      <DasType>bigint|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="635" parent="330" name="news">
      <DasType>varchar(200)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="636" parent="330" name="create_dept">
      <DasType>varchar(100)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="637" parent="330" name="create_by">
      <DasType>varchar(100)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="638" parent="330" name="create_time">
      <DasType>varchar(100)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="639" parent="330" name="update_by">
      <DasType>varchar(100)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="640" parent="330" name="update_time">
      <DasType>varchar(100)|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="641" parent="330" name="type">
      <DasType>int|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="642" parent="330" name="isread">
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>11</Position>
    </column>
    <column id="643" parent="331" name="news_id">
      <DasType>bigint|0s</DasType>
      <Position>1</Position>
    </column>
    <column id="644" parent="331" name="commernt_id">
      <DasType>bigint|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="645" parent="331" name="create_dept">
      <DasType>varchar(100)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="646" parent="331" name="create_by">
      <DasType>varchar(100)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="647" parent="331" name="create_time">
      <DasType>varchar(100)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="648" parent="331" name="update_by">
      <DasType>varchar(100)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="649" parent="331" name="update_time">
      <DasType>varchar(100)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="650" parent="332" name="id">
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="651" parent="332" name="name">
      <DasType>varchar(100)|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>2</Position>
    </column>
    <column id="652" parent="332" name="flag">
      <DasType>varchar(10)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="653" parent="332" name="create_dept">
      <DasType>varchar(10)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="654" parent="332" name="create_by">
      <DasType>varchar(10)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="655" parent="332" name="create_time">
      <DasType>varchar(50)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="656" parent="332" name="update_by">
      <DasType>varchar(50)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="657" parent="332" name="update_time">
      <DasType>varchar(50)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="658" parent="332" name="author">
      <DasType>varchar(100)|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="659" parent="333" name="id">
      <DasType>bigint|0s</DasType>
      <Position>1</Position>
    </column>
    <column id="660" parent="333" name="grades">
      <DasType>int|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="661" parent="333" name="type">
      <DasType>varchar(10)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="662" parent="333" name="platform">
      <DasType>varchar(10)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="663" parent="333" name="create_dept">
      <DasType>varchar(100)|0s</DasType>
      <Hidden>1</Hidden>
      <Position>5</Position>
    </column>
    <column id="664" parent="333" name="create_by">
      <DasType>varchar(100)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="665" parent="333" name="create_time">
      <DasType>varchar(100)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="666" parent="333" name="update_by">
      <DasType>varchar(100)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="667" parent="333" name="update_time">
      <DasType>varchar(100)|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="668" parent="333" name="summary">
      <DasType>varchar(200)|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="669" parent="333" name="icon">
      <DasType>varchar(200)|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="670" parent="333" name="look">
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>12</Position>
    </column>
    <column id="671" parent="334" name="id">
      <DasType>bigint|0s</DasType>
      <Position>1</Position>
    </column>
    <column id="672" parent="334" name="chaptername">
      <DasType>varchar(100)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="673" parent="334" name="chapter">
      <DasType>int|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="674" parent="334" name="link">
      <DasType>bigint|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="675" parent="334" name="create_dept">
      <DasType>varchar(100)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="676" parent="334" name="create_by">
      <DasType>varchar(100)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="677" parent="334" name="create_time">
      <DasType>varchar(100)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="678" parent="334" name="update_by">
      <DasType>varchar(100)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="679" parent="334" name="update_time">
      <DasType>varchar(100)|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="680" parent="335" name="id">
      <DasType>int|0s</DasType>
      <Position>1</Position>
    </column>
    <column id="681" parent="335" name="userid">
      <DasType>varchar(100)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="682" parent="335" name="noval_name">
      <DasType>varchar(100)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="683" parent="335" name="noval_chapter">
      <DasType>varchar(100)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="684" parent="335" name="noval_content">
      <DasType>varchar(300)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="685" parent="335" name="exegesis">
      <DasType>varchar(200)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="686" parent="335" name="create_dept">
      <DasType>varchar(100)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="687" parent="335" name="create_by">
      <DasType>varchar(100)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="688" parent="335" name="create_time">
      <DasType>varchar(100)|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="689" parent="335" name="update_by">
      <DasType>varchar(100)|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="690" parent="335" name="update_time">
      <DasType>varchar(100)|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="691" parent="335" name="noval_id">
      <DasType>int|0s</DasType>
      <Position>12</Position>
    </column>
    <column id="692" parent="336" name="id">
      <AutoIncrement>1</AutoIncrement>
      <Comment>主键ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="693" parent="336" name="book_id">
      <Comment>小说ID</Comment>
      <DasType>varchar(64)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="694" parent="336" name="book_name">
      <Comment>小说名称</Comment>
      <DasType>varchar(255)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="695" parent="336" name="plot">
      <Comment>剧情大纲</Comment>
      <DasType>longtext|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="696" parent="336" name="writing_style">
      <Comment>文风特色</Comment>
      <DasType>text|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="697" parent="336" name="characters">
      <Comment>人物设定</Comment>
      <DasType>text|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="698" parent="336" name="full_outline">
      <Comment>完整大纲内容</Comment>
      <DasType>longtext|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="699" parent="336" name="offset_chapter">
      <Comment>大纲提取进度（已处理到第几章）</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>8</Position>
    </column>
    <column id="700" parent="336" name="create_dept">
      <Comment>创建部门</Comment>
      <DasType>bigint|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="701" parent="336" name="create_by">
      <Comment>创建者</Comment>
      <DasType>varchar(64)|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="702" parent="336" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="703" parent="336" name="update_by">
      <Comment>更新者</Comment>
      <DasType>varchar(64)|0s</DasType>
      <Position>12</Position>
    </column>
    <column id="704" parent="336" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>13</Position>
    </column>
    <column id="705" parent="336" name="remark">
      <Comment>备注</Comment>
      <DasType>varchar(500)|0s</DasType>
      <Position>14</Position>
    </column>
    <column id="706" parent="336" name="del_flag">
      <Comment>删除标志（0代表存在 2代表删除）</Comment>
      <DasType>char(1)|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>15</Position>
    </column>
    <index id="707" parent="336" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="708" parent="336" name="uk_book_id">
      <ColNames>book_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="709" parent="336" name="idx_book_name">
      <ColNames>book_name</ColNames>
      <Type>btree</Type>
    </index>
    <index id="710" parent="336" name="idx_create_time">
      <ColNames>create_time</ColNames>
      <Type>btree</Type>
    </index>
    <key id="711" parent="336" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="712" parent="336" name="uk_book_id">
      <UnderlyingIndexName>uk_book_id</UnderlyingIndexName>
    </key>
    <column id="713" parent="337" name="config_id">
      <Comment>参数主键</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="714" parent="337" name="tenant_id">
      <Comment>租户编号</Comment>
      <DasType>varchar(20)|0s</DasType>
      <DefaultExpression>&apos;000000&apos;</DefaultExpression>
      <Position>2</Position>
    </column>
    <column id="715" parent="337" name="config_name">
      <Comment>参数名称</Comment>
      <DasType>varchar(100)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>3</Position>
    </column>
    <column id="716" parent="337" name="config_key">
      <Comment>参数键名</Comment>
      <DasType>varchar(100)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>4</Position>
    </column>
    <column id="717" parent="337" name="config_value">
      <Comment>参数键值</Comment>
      <DasType>varchar(500)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>5</Position>
    </column>
    <column id="718" parent="337" name="config_type">
      <Comment>系统内置（Y是 N否）</Comment>
      <DasType>char(1)|0s</DasType>
      <DefaultExpression>&apos;N&apos;</DefaultExpression>
      <Position>6</Position>
    </column>
    <column id="719" parent="337" name="create_dept">
      <Comment>创建部门</Comment>
      <DasType>bigint|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="720" parent="337" name="create_by">
      <Comment>创建者</Comment>
      <DasType>bigint|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="721" parent="337" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="722" parent="337" name="update_by">
      <Comment>更新者</Comment>
      <DasType>bigint|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="723" parent="337" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="724" parent="337" name="remark">
      <Comment>备注</Comment>
      <DasType>varchar(500)|0s</DasType>
      <Position>12</Position>
    </column>
    <index id="725" parent="337" name="PRIMARY">
      <ColNames>config_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="726" parent="337" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="727" parent="338" name="dept_id">
      <Comment>部门id</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="728" parent="338" name="tenant_id">
      <Comment>租户编号</Comment>
      <DasType>varchar(20)|0s</DasType>
      <DefaultExpression>&apos;000000&apos;</DefaultExpression>
      <Position>2</Position>
    </column>
    <column id="729" parent="338" name="parent_id">
      <Comment>父部门id</Comment>
      <DasType>bigint|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>3</Position>
    </column>
    <column id="730" parent="338" name="ancestors">
      <Comment>祖级列表</Comment>
      <DasType>varchar(500)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>4</Position>
    </column>
    <column id="731" parent="338" name="dept_name">
      <Comment>部门名称</Comment>
      <DasType>varchar(30)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>5</Position>
    </column>
    <column id="732" parent="338" name="order_num">
      <Comment>显示顺序</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>6</Position>
    </column>
    <column id="733" parent="338" name="leader">
      <Comment>负责人</Comment>
      <DasType>varchar(20)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="734" parent="338" name="phone">
      <Comment>联系电话</Comment>
      <DasType>varchar(11)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="735" parent="338" name="email">
      <Comment>邮箱</Comment>
      <DasType>varchar(50)|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="736" parent="338" name="status">
      <Comment>部门状态（0正常 1停用）</Comment>
      <DasType>char(1)|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>10</Position>
    </column>
    <column id="737" parent="338" name="del_flag">
      <Comment>删除标志（0代表存在 2代表删除）</Comment>
      <DasType>char(1)|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>11</Position>
    </column>
    <column id="738" parent="338" name="create_dept">
      <Comment>创建部门</Comment>
      <DasType>bigint|0s</DasType>
      <Position>12</Position>
    </column>
    <column id="739" parent="338" name="create_by">
      <Comment>创建者</Comment>
      <DasType>bigint|0s</DasType>
      <Position>13</Position>
    </column>
    <column id="740" parent="338" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>14</Position>
    </column>
    <column id="741" parent="338" name="update_by">
      <Comment>更新者</Comment>
      <DasType>bigint|0s</DasType>
      <Position>15</Position>
    </column>
    <column id="742" parent="338" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>16</Position>
    </column>
    <index id="743" parent="338" name="PRIMARY">
      <ColNames>dept_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="744" parent="338" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="745" parent="339" name="dict_code">
      <Comment>字典编码</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="746" parent="339" name="tenant_id">
      <Comment>租户编号</Comment>
      <DasType>varchar(20)|0s</DasType>
      <DefaultExpression>&apos;000000&apos;</DefaultExpression>
      <Position>2</Position>
    </column>
    <column id="747" parent="339" name="dict_sort">
      <Comment>字典排序</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>3</Position>
    </column>
    <column id="748" parent="339" name="dict_label">
      <Comment>字典标签</Comment>
      <DasType>varchar(100)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>4</Position>
    </column>
    <column id="749" parent="339" name="dict_value">
      <Comment>字典键值</Comment>
      <DasType>varchar(100)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>5</Position>
    </column>
    <column id="750" parent="339" name="dict_type">
      <Comment>字典类型</Comment>
      <DasType>varchar(100)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>6</Position>
    </column>
    <column id="751" parent="339" name="css_class">
      <Comment>样式属性（其他样式扩展）</Comment>
      <DasType>varchar(100)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="752" parent="339" name="list_class">
      <Comment>表格回显样式</Comment>
      <DasType>varchar(100)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="753" parent="339" name="is_default">
      <Comment>是否默认（Y是 N否）</Comment>
      <DasType>char(1)|0s</DasType>
      <DefaultExpression>&apos;N&apos;</DefaultExpression>
      <Position>9</Position>
    </column>
    <column id="754" parent="339" name="status">
      <Comment>状态（0正常 1停用）</Comment>
      <DasType>char(1)|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>10</Position>
    </column>
    <column id="755" parent="339" name="create_dept">
      <Comment>创建部门</Comment>
      <DasType>bigint|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="756" parent="339" name="create_by">
      <Comment>创建者</Comment>
      <DasType>bigint|0s</DasType>
      <Position>12</Position>
    </column>
    <column id="757" parent="339" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>13</Position>
    </column>
    <column id="758" parent="339" name="update_by">
      <Comment>更新者</Comment>
      <DasType>bigint|0s</DasType>
      <Position>14</Position>
    </column>
    <column id="759" parent="339" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>15</Position>
    </column>
    <column id="760" parent="339" name="remark">
      <Comment>备注</Comment>
      <DasType>varchar(500)|0s</DasType>
      <Position>16</Position>
    </column>
    <index id="761" parent="339" name="PRIMARY">
      <ColNames>dict_code</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="762" parent="339" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="763" parent="340" name="dict_id">
      <Comment>字典主键</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="764" parent="340" name="tenant_id">
      <Comment>租户编号</Comment>
      <DasType>varchar(20)|0s</DasType>
      <DefaultExpression>&apos;000000&apos;</DefaultExpression>
      <Position>2</Position>
    </column>
    <column id="765" parent="340" name="dict_name">
      <Comment>字典名称</Comment>
      <DasType>varchar(100)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>3</Position>
    </column>
    <column id="766" parent="340" name="dict_type">
      <Comment>字典类型</Comment>
      <DasType>varchar(100)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>4</Position>
    </column>
    <column id="767" parent="340" name="status">
      <Comment>状态（0正常 1停用）</Comment>
      <DasType>char(1)|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>5</Position>
    </column>
    <column id="768" parent="340" name="create_dept">
      <Comment>创建部门</Comment>
      <DasType>bigint|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="769" parent="340" name="create_by">
      <Comment>创建者</Comment>
      <DasType>bigint|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="770" parent="340" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="771" parent="340" name="update_by">
      <Comment>更新者</Comment>
      <DasType>bigint|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="772" parent="340" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="773" parent="340" name="remark">
      <Comment>备注</Comment>
      <DasType>varchar(500)|0s</DasType>
      <Position>11</Position>
    </column>
    <index id="774" parent="340" name="PRIMARY">
      <ColNames>dict_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="775" parent="340" name="tenant_id">
      <ColNames>tenant_id
dict_type</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="776" parent="340" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="777" parent="340" name="tenant_id">
      <UnderlyingIndexName>tenant_id</UnderlyingIndexName>
    </key>
    <column id="778" parent="341" name="id">
      <AutoIncrement>1</AutoIncrement>
      <Comment>文件id</Comment>
      <DasType>bigint unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="779" parent="341" name="url">
      <Comment>文件访问地址</Comment>
      <DasType>varchar(512)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="780" parent="341" name="size">
      <Comment>文件大小，单位字节</Comment>
      <DasType>bigint|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="781" parent="341" name="filename">
      <Comment>文件名称</Comment>
      <DasType>varchar(256)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="782" parent="341" name="original_filename">
      <Comment>原始文件名</Comment>
      <DasType>varchar(256)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="783" parent="341" name="base_path">
      <Comment>基础存储路径</Comment>
      <DasType>varchar(256)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="784" parent="341" name="path">
      <Comment>存储路径</Comment>
      <DasType>varchar(256)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="785" parent="341" name="ext">
      <Comment>文件扩展名</Comment>
      <DasType>varchar(32)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="786" parent="341" name="user_id">
      <Comment>文件所属用户</Comment>
      <DasType>varchar(32)|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="787" parent="341" name="file_type">
      <Comment>文件类型</Comment>
      <DasType>varchar(32)|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="788" parent="341" name="attr">
      <Comment>附加属性</Comment>
      <DasType>text|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="789" parent="341" name="create_dept">
      <Comment>创建部门</Comment>
      <DasType>bigint|0s</DasType>
      <Position>12</Position>
    </column>
    <column id="790" parent="341" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>13</Position>
    </column>
    <column id="791" parent="341" name="create_by">
      <Comment>创建者</Comment>
      <DasType>varchar(64)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>14</Position>
      <CollationName>utf8mb4_general_ci</CollationName>
    </column>
    <column id="792" parent="341" name="update_by">
      <Comment>更新者</Comment>
      <DasType>varchar(64)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>15</Position>
      <CollationName>utf8mb4_general_ci</CollationName>
    </column>
    <column id="793" parent="341" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>16</Position>
    </column>
    <column id="794" parent="341" name="remark">
      <Comment>备注</Comment>
      <DasType>varchar(500)|0s</DasType>
      <Position>17</Position>
      <CollationName>utf8mb4_general_ci</CollationName>
    </column>
    <column id="795" parent="341" name="version">
      <Comment>版本</Comment>
      <DasType>int|0s</DasType>
      <Position>18</Position>
    </column>
    <column id="796" parent="341" name="del_flag">
      <Comment>删除标志（0代表存在 1代表删除）</Comment>
      <DasType>char(1)|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>19</Position>
      <CollationName>utf8mb4_general_ci</CollationName>
    </column>
    <column id="797" parent="341" name="update_ip">
      <Comment>更新IP</Comment>
      <DasType>varchar(128)|0s</DasType>
      <Position>20</Position>
      <CollationName>utf8mb4_general_ci</CollationName>
    </column>
    <column id="798" parent="341" name="tenant_id">
      <Comment>租户Id</Comment>
      <DasType>bigint|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>21</Position>
    </column>
    <index id="799" parent="341" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="800" parent="341" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="801" parent="342" name="info_id">
      <Comment>访问ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="802" parent="342" name="tenant_id">
      <Comment>租户编号</Comment>
      <DasType>varchar(20)|0s</DasType>
      <DefaultExpression>&apos;000000&apos;</DefaultExpression>
      <Position>2</Position>
    </column>
    <column id="803" parent="342" name="user_name">
      <Comment>用户账号</Comment>
      <DasType>varchar(50)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>3</Position>
    </column>
    <column id="804" parent="342" name="ipaddr">
      <Comment>登录IP地址</Comment>
      <DasType>varchar(128)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>4</Position>
    </column>
    <column id="805" parent="342" name="login_location">
      <Comment>登录地点</Comment>
      <DasType>varchar(255)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>5</Position>
    </column>
    <column id="806" parent="342" name="browser">
      <Comment>浏览器类型</Comment>
      <DasType>varchar(50)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>6</Position>
    </column>
    <column id="807" parent="342" name="os">
      <Comment>操作系统</Comment>
      <DasType>varchar(50)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>7</Position>
    </column>
    <column id="808" parent="342" name="status">
      <Comment>登录状态（0成功 1失败）</Comment>
      <DasType>char(1)|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>8</Position>
    </column>
    <column id="809" parent="342" name="msg">
      <Comment>提示消息</Comment>
      <DasType>varchar(255)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>9</Position>
    </column>
    <column id="810" parent="342" name="login_time">
      <Comment>访问时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>10</Position>
    </column>
    <index id="811" parent="342" name="PRIMARY">
      <ColNames>info_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="812" parent="342" name="idx_sys_logininfor_s">
      <ColNames>status</ColNames>
      <Type>btree</Type>
    </index>
    <index id="813" parent="342" name="idx_sys_logininfor_lt">
      <ColNames>login_time</ColNames>
      <Type>btree</Type>
    </index>
    <key id="814" parent="342" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="815" parent="343" name="menu_id">
      <Comment>菜单ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="816" parent="343" name="menu_name">
      <Comment>菜单名称</Comment>
      <DasType>varchar(50)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="817" parent="343" name="parent_id">
      <Comment>父菜单ID</Comment>
      <DasType>bigint|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>3</Position>
    </column>
    <column id="818" parent="343" name="order_num">
      <Comment>显示顺序</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>4</Position>
    </column>
    <column id="819" parent="343" name="path">
      <Comment>路由地址</Comment>
      <DasType>varchar(200)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>5</Position>
    </column>
    <column id="820" parent="343" name="component">
      <Comment>组件路径</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="821" parent="343" name="query_param">
      <Comment>路由参数</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="822" parent="343" name="is_frame">
      <Comment>是否为外链（0是 1否）</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <Position>8</Position>
    </column>
    <column id="823" parent="343" name="is_cache">
      <Comment>是否缓存（0缓存 1不缓存）</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>9</Position>
    </column>
    <column id="824" parent="343" name="menu_type">
      <Comment>菜单类型（M目录 C菜单 F按钮）</Comment>
      <DasType>char(1)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>10</Position>
    </column>
    <column id="825" parent="343" name="visible">
      <Comment>显示状态（0显示 1隐藏）</Comment>
      <DasType>char(1)|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>11</Position>
    </column>
    <column id="826" parent="343" name="status">
      <Comment>菜单状态（0正常 1停用）</Comment>
      <DasType>char(1)|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>12</Position>
    </column>
    <column id="827" parent="343" name="perms">
      <Comment>权限标识</Comment>
      <DasType>varchar(100)|0s</DasType>
      <Position>13</Position>
    </column>
    <column id="828" parent="343" name="icon">
      <Comment>菜单图标</Comment>
      <DasType>varchar(100)|0s</DasType>
      <DefaultExpression>&apos;#&apos;</DefaultExpression>
      <Position>14</Position>
    </column>
    <column id="829" parent="343" name="create_dept">
      <Comment>创建部门</Comment>
      <DasType>bigint|0s</DasType>
      <Position>15</Position>
    </column>
    <column id="830" parent="343" name="create_by">
      <Comment>创建者</Comment>
      <DasType>bigint|0s</DasType>
      <Position>16</Position>
    </column>
    <column id="831" parent="343" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>17</Position>
    </column>
    <column id="832" parent="343" name="update_by">
      <Comment>更新者</Comment>
      <DasType>bigint|0s</DasType>
      <Position>18</Position>
    </column>
    <column id="833" parent="343" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>19</Position>
    </column>
    <column id="834" parent="343" name="remark">
      <Comment>备注</Comment>
      <DasType>varchar(500)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>20</Position>
    </column>
    <index id="835" parent="343" name="PRIMARY">
      <ColNames>menu_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="836" parent="343" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="837" parent="344" name="notice_id">
      <Comment>公告ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="838" parent="344" name="tenant_id">
      <Comment>租户编号</Comment>
      <DasType>varchar(20)|0s</DasType>
      <DefaultExpression>&apos;000000&apos;</DefaultExpression>
      <Position>2</Position>
    </column>
    <column id="839" parent="344" name="notice_title">
      <Comment>公告标题</Comment>
      <DasType>varchar(50)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="840" parent="344" name="notice_type">
      <Comment>公告类型（1通知 2公告）</Comment>
      <DasType>char(1)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="841" parent="344" name="notice_content">
      <Comment>公告内容</Comment>
      <DasType>longblob|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="842" parent="344" name="status">
      <Comment>公告状态（0正常 1关闭）</Comment>
      <DasType>char(1)|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>6</Position>
    </column>
    <column id="843" parent="344" name="create_dept">
      <Comment>创建部门</Comment>
      <DasType>bigint|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="844" parent="344" name="create_by">
      <Comment>创建者</Comment>
      <DasType>bigint|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="845" parent="344" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="846" parent="344" name="update_by">
      <Comment>更新者</Comment>
      <DasType>bigint|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="847" parent="344" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="848" parent="344" name="remark">
      <Comment>备注</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>12</Position>
    </column>
    <index id="849" parent="344" name="PRIMARY">
      <ColNames>notice_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="850" parent="344" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="851" parent="345" name="id">
      <Comment>ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="852" parent="345" name="user_id">
      <Comment>用户ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="853" parent="345" name="notice_id">
      <Comment>公告ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="854" parent="345" name="read_status">
      <Comment>阅读状态（0未读 1已读）</Comment>
      <DasType>char(1)|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>4</Position>
    </column>
    <column id="855" parent="345" name="create_dept">
      <Comment>创建部门</Comment>
      <DasType>bigint|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="856" parent="345" name="create_by">
      <Comment>创建者</Comment>
      <DasType>bigint|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="857" parent="345" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="858" parent="345" name="update_by">
      <Comment>更新者</Comment>
      <DasType>bigint|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="859" parent="345" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="860" parent="345" name="remark">
      <Comment>备注</Comment>
      <DasType>varchar(500)|0s</DasType>
      <Position>10</Position>
    </column>
    <index id="861" parent="345" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="862" parent="345" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="863" parent="346" name="oper_id">
      <Comment>日志主键</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="864" parent="346" name="tenant_id">
      <Comment>租户编号</Comment>
      <DasType>varchar(20)|0s</DasType>
      <DefaultExpression>&apos;000000&apos;</DefaultExpression>
      <Position>2</Position>
    </column>
    <column id="865" parent="346" name="title">
      <Comment>模块标题</Comment>
      <DasType>varchar(50)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>3</Position>
    </column>
    <column id="866" parent="346" name="business_type">
      <Comment>业务类型（0其它 1新增 2修改 3删除）</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>4</Position>
    </column>
    <column id="867" parent="346" name="method">
      <Comment>方法名称</Comment>
      <DasType>varchar(100)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>5</Position>
    </column>
    <column id="868" parent="346" name="request_method">
      <Comment>请求方式</Comment>
      <DasType>varchar(10)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>6</Position>
    </column>
    <column id="869" parent="346" name="operator_type">
      <Comment>操作类别（0其它 1后台用户 2手机端用户）</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>7</Position>
    </column>
    <column id="870" parent="346" name="oper_name">
      <Comment>操作人员</Comment>
      <DasType>varchar(50)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>8</Position>
    </column>
    <column id="871" parent="346" name="dept_name">
      <Comment>部门名称</Comment>
      <DasType>varchar(50)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>9</Position>
    </column>
    <column id="872" parent="346" name="oper_url">
      <Comment>请求URL</Comment>
      <DasType>varchar(255)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>10</Position>
    </column>
    <column id="873" parent="346" name="oper_ip">
      <Comment>主机地址</Comment>
      <DasType>varchar(128)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>11</Position>
    </column>
    <column id="874" parent="346" name="oper_location">
      <Comment>操作地点</Comment>
      <DasType>varchar(255)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>12</Position>
    </column>
    <column id="875" parent="346" name="oper_param">
      <Comment>请求参数</Comment>
      <DasType>varchar(2000)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>13</Position>
    </column>
    <column id="876" parent="346" name="json_result">
      <Comment>返回参数</Comment>
      <DasType>varchar(2000)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>14</Position>
    </column>
    <column id="877" parent="346" name="status">
      <Comment>操作状态（0正常 1异常）</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>15</Position>
    </column>
    <column id="878" parent="346" name="error_msg">
      <Comment>错误消息</Comment>
      <DasType>varchar(2000)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>16</Position>
    </column>
    <column id="879" parent="346" name="oper_time">
      <Comment>操作时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>17</Position>
    </column>
    <column id="880" parent="346" name="cost_time">
      <Comment>消耗时间</Comment>
      <DasType>bigint|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>18</Position>
    </column>
    <index id="881" parent="346" name="PRIMARY">
      <ColNames>oper_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="882" parent="346" name="idx_sys_oper_log_bt">
      <ColNames>business_type</ColNames>
      <Type>btree</Type>
    </index>
    <index id="883" parent="346" name="idx_sys_oper_log_s">
      <ColNames>status</ColNames>
      <Type>btree</Type>
    </index>
    <index id="884" parent="346" name="idx_sys_oper_log_ot">
      <ColNames>oper_time</ColNames>
      <Type>btree</Type>
    </index>
    <key id="885" parent="346" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="886" parent="347" name="oss_id">
      <Comment>对象存储主键</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="887" parent="347" name="tenant_id">
      <Comment>租户编号</Comment>
      <DasType>varchar(20)|0s</DasType>
      <DefaultExpression>&apos;000000&apos;</DefaultExpression>
      <Position>2</Position>
    </column>
    <column id="888" parent="347" name="file_name">
      <Comment>文件名</Comment>
      <DasType>varchar(255)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="889" parent="347" name="original_name">
      <Comment>原名</Comment>
      <DasType>varchar(255)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="890" parent="347" name="file_suffix">
      <Comment>文件后缀名</Comment>
      <DasType>varchar(10)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="891" parent="347" name="url">
      <Comment>URL地址</Comment>
      <DasType>varchar(500)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="892" parent="347" name="create_dept">
      <Comment>创建部门</Comment>
      <DasType>bigint|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="893" parent="347" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="894" parent="347" name="create_by">
      <Comment>上传人</Comment>
      <DasType>bigint|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="895" parent="347" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="896" parent="347" name="update_by">
      <Comment>更新人</Comment>
      <DasType>bigint|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="897" parent="347" name="service">
      <Comment>服务商</Comment>
      <DasType>varchar(20)|0s</DasType>
      <DefaultExpression>&apos;minio&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>12</Position>
    </column>
    <index id="898" parent="347" name="PRIMARY">
      <ColNames>oss_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="899" parent="347" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="900" parent="348" name="oss_config_id">
      <Comment>主建</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="901" parent="348" name="tenant_id">
      <Comment>租户编号</Comment>
      <DasType>varchar(20)|0s</DasType>
      <DefaultExpression>&apos;000000&apos;</DefaultExpression>
      <Position>2</Position>
    </column>
    <column id="902" parent="348" name="config_key">
      <Comment>配置key</Comment>
      <DasType>varchar(20)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="903" parent="348" name="access_key">
      <Comment>accessKey</Comment>
      <DasType>varchar(255)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>4</Position>
    </column>
    <column id="904" parent="348" name="secret_key">
      <Comment>秘钥</Comment>
      <DasType>varchar(255)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>5</Position>
    </column>
    <column id="905" parent="348" name="bucket_name">
      <Comment>桶名称</Comment>
      <DasType>varchar(255)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>6</Position>
    </column>
    <column id="906" parent="348" name="prefix">
      <Comment>前缀</Comment>
      <DasType>varchar(255)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>7</Position>
    </column>
    <column id="907" parent="348" name="endpoint">
      <Comment>访问站点</Comment>
      <DasType>varchar(255)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>8</Position>
    </column>
    <column id="908" parent="348" name="domain">
      <Comment>自定义域名</Comment>
      <DasType>varchar(255)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>9</Position>
    </column>
    <column id="909" parent="348" name="is_https">
      <Comment>是否https（Y=是,N=否）</Comment>
      <DasType>char(1)|0s</DasType>
      <DefaultExpression>&apos;N&apos;</DefaultExpression>
      <Position>10</Position>
    </column>
    <column id="910" parent="348" name="region">
      <Comment>域</Comment>
      <DasType>varchar(255)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>11</Position>
    </column>
    <column id="911" parent="348" name="access_policy">
      <Comment>桶权限类型(0=private 1=public 2=custom)</Comment>
      <DasType>char(1)|0s</DasType>
      <DefaultExpression>&apos;1&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>12</Position>
    </column>
    <column id="912" parent="348" name="status">
      <Comment>是否默认（0=是,1=否）</Comment>
      <DasType>char(1)|0s</DasType>
      <DefaultExpression>&apos;1&apos;</DefaultExpression>
      <Position>13</Position>
    </column>
    <column id="913" parent="348" name="ext1">
      <Comment>扩展字段</Comment>
      <DasType>varchar(255)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>14</Position>
    </column>
    <column id="914" parent="348" name="create_dept">
      <Comment>创建部门</Comment>
      <DasType>bigint|0s</DasType>
      <Position>15</Position>
    </column>
    <column id="915" parent="348" name="create_by">
      <Comment>创建者</Comment>
      <DasType>bigint|0s</DasType>
      <Position>16</Position>
    </column>
    <column id="916" parent="348" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>17</Position>
    </column>
    <column id="917" parent="348" name="update_by">
      <Comment>更新者</Comment>
      <DasType>bigint|0s</DasType>
      <Position>18</Position>
    </column>
    <column id="918" parent="348" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>19</Position>
    </column>
    <column id="919" parent="348" name="remark">
      <Comment>备注</Comment>
      <DasType>varchar(500)|0s</DasType>
      <Position>20</Position>
    </column>
    <index id="920" parent="348" name="PRIMARY">
      <ColNames>oss_config_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="921" parent="348" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="922" parent="349" name="post_id">
      <Comment>岗位ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="923" parent="349" name="tenant_id">
      <Comment>租户编号</Comment>
      <DasType>varchar(20)|0s</DasType>
      <DefaultExpression>&apos;000000&apos;</DefaultExpression>
      <Position>2</Position>
    </column>
    <column id="924" parent="349" name="post_code">
      <Comment>岗位编码</Comment>
      <DasType>varchar(64)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="925" parent="349" name="post_name">
      <Comment>岗位名称</Comment>
      <DasType>varchar(50)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="926" parent="349" name="post_sort">
      <Comment>显示顺序</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="927" parent="349" name="status">
      <Comment>状态（0正常 1停用）</Comment>
      <DasType>char(1)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="928" parent="349" name="create_dept">
      <Comment>创建部门</Comment>
      <DasType>bigint|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="929" parent="349" name="create_by">
      <Comment>创建者</Comment>
      <DasType>bigint|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="930" parent="349" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="931" parent="349" name="update_by">
      <Comment>更新者</Comment>
      <DasType>bigint|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="932" parent="349" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="933" parent="349" name="remark">
      <Comment>备注</Comment>
      <DasType>varchar(500)|0s</DasType>
      <Position>12</Position>
    </column>
    <index id="934" parent="349" name="PRIMARY">
      <ColNames>post_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="935" parent="349" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="936" parent="350" name="role_id">
      <Comment>角色ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="937" parent="350" name="tenant_id">
      <Comment>租户编号</Comment>
      <DasType>varchar(20)|0s</DasType>
      <DefaultExpression>&apos;000000&apos;</DefaultExpression>
      <Position>2</Position>
    </column>
    <column id="938" parent="350" name="role_name">
      <Comment>角色名称</Comment>
      <DasType>varchar(30)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="939" parent="350" name="role_key">
      <Comment>角色权限字符串</Comment>
      <DasType>varchar(100)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="940" parent="350" name="role_sort">
      <Comment>显示顺序</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="941" parent="350" name="data_scope">
      <Comment>数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限）</Comment>
      <DasType>char(1)|0s</DasType>
      <DefaultExpression>&apos;1&apos;</DefaultExpression>
      <Position>6</Position>
    </column>
    <column id="942" parent="350" name="menu_check_strictly">
      <Comment>菜单树选择项是否关联显示</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <Position>7</Position>
    </column>
    <column id="943" parent="350" name="dept_check_strictly">
      <Comment>部门树选择项是否关联显示</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <Position>8</Position>
    </column>
    <column id="944" parent="350" name="status">
      <Comment>角色状态（0正常 1停用）</Comment>
      <DasType>char(1)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>9</Position>
    </column>
    <column id="945" parent="350" name="del_flag">
      <Comment>删除标志（0代表存在 2代表删除）</Comment>
      <DasType>char(1)|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>10</Position>
    </column>
    <column id="946" parent="350" name="create_dept">
      <Comment>创建部门</Comment>
      <DasType>bigint|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="947" parent="350" name="create_by">
      <Comment>创建者</Comment>
      <DasType>bigint|0s</DasType>
      <Position>12</Position>
    </column>
    <column id="948" parent="350" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>13</Position>
    </column>
    <column id="949" parent="350" name="update_by">
      <Comment>更新者</Comment>
      <DasType>bigint|0s</DasType>
      <Position>14</Position>
    </column>
    <column id="950" parent="350" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>15</Position>
    </column>
    <column id="951" parent="350" name="remark">
      <Comment>备注</Comment>
      <DasType>varchar(500)|0s</DasType>
      <Position>16</Position>
    </column>
    <index id="952" parent="350" name="PRIMARY">
      <ColNames>role_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="953" parent="350" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="954" parent="351" name="role_id">
      <Comment>角色ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="955" parent="351" name="dept_id">
      <Comment>部门ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <index id="956" parent="351" name="PRIMARY">
      <ColNames>role_id
dept_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="957" parent="351" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="958" parent="352" name="role_id">
      <Comment>角色ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="959" parent="352" name="menu_id">
      <Comment>菜单ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <index id="960" parent="352" name="PRIMARY">
      <ColNames>role_id
menu_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="961" parent="352" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="962" parent="353" name="id">
      <Comment>id</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="963" parent="353" name="tenant_id">
      <Comment>租户编号</Comment>
      <DasType>varchar(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="964" parent="353" name="contact_user_name">
      <Comment>联系人</Comment>
      <DasType>varchar(20)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="965" parent="353" name="contact_phone">
      <Comment>联系电话</Comment>
      <DasType>varchar(20)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="966" parent="353" name="company_name">
      <Comment>企业名称</Comment>
      <DasType>varchar(50)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="967" parent="353" name="license_number">
      <Comment>统一社会信用代码</Comment>
      <DasType>varchar(30)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="968" parent="353" name="address">
      <Comment>地址</Comment>
      <DasType>varchar(200)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="969" parent="353" name="intro">
      <Comment>企业简介</Comment>
      <DasType>varchar(200)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="970" parent="353" name="domain">
      <Comment>域名</Comment>
      <DasType>varchar(200)|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="971" parent="353" name="remark">
      <Comment>备注</Comment>
      <DasType>varchar(200)|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="972" parent="353" name="package_id">
      <Comment>租户套餐编号</Comment>
      <DasType>bigint|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="973" parent="353" name="expire_time">
      <Comment>过期时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>12</Position>
    </column>
    <column id="974" parent="353" name="account_count">
      <Comment>用户数量（-1不限制）</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>-1</DefaultExpression>
      <Position>13</Position>
    </column>
    <column id="975" parent="353" name="status">
      <Comment>租户状态（0正常 1停用）</Comment>
      <DasType>char(1)|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>14</Position>
    </column>
    <column id="976" parent="353" name="del_flag">
      <Comment>删除标志（0代表存在 2代表删除）</Comment>
      <DasType>char(1)|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>15</Position>
    </column>
    <column id="977" parent="353" name="create_dept">
      <Comment>创建部门</Comment>
      <DasType>bigint|0s</DasType>
      <Position>16</Position>
    </column>
    <column id="978" parent="353" name="create_by">
      <Comment>创建者</Comment>
      <DasType>bigint|0s</DasType>
      <Position>17</Position>
    </column>
    <column id="979" parent="353" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>18</Position>
    </column>
    <column id="980" parent="353" name="update_by">
      <Comment>更新者</Comment>
      <DasType>bigint|0s</DasType>
      <Position>19</Position>
    </column>
    <column id="981" parent="353" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>20</Position>
    </column>
    <index id="982" parent="353" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="983" parent="353" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="984" parent="354" name="package_id">
      <Comment>租户套餐id</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="985" parent="354" name="package_name">
      <Comment>套餐名称</Comment>
      <DasType>varchar(20)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="986" parent="354" name="menu_ids">
      <Comment>关联菜单id</Comment>
      <DasType>varchar(3000)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="987" parent="354" name="remark">
      <Comment>备注</Comment>
      <DasType>varchar(200)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="988" parent="354" name="menu_check_strictly">
      <Comment>菜单树选择项是否关联显示</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <Position>5</Position>
    </column>
    <column id="989" parent="354" name="status">
      <Comment>状态（0正常 1停用）</Comment>
      <DasType>char(1)|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>6</Position>
    </column>
    <column id="990" parent="354" name="del_flag">
      <Comment>删除标志（0代表存在 2代表删除）</Comment>
      <DasType>char(1)|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>7</Position>
    </column>
    <column id="991" parent="354" name="create_dept">
      <Comment>创建部门</Comment>
      <DasType>bigint|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="992" parent="354" name="create_by">
      <Comment>创建者</Comment>
      <DasType>bigint|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="993" parent="354" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="994" parent="354" name="update_by">
      <Comment>更新者</Comment>
      <DasType>bigint|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="995" parent="354" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>12</Position>
    </column>
    <index id="996" parent="354" name="PRIMARY">
      <ColNames>package_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="997" parent="354" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="998" parent="355" name="user_id">
      <Comment>用户ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="999" parent="355" name="open_id">
      <Comment>微信用户标识</Comment>
      <DasType>varchar(100)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="1000" parent="355" name="user_grade">
      <Comment>用户等级</Comment>
      <DasType>char(1)|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>3</Position>
    </column>
    <column id="1001" parent="355" name="user_balance">
      <Comment>账户余额</Comment>
      <DasType>double(20,2 digit)|0s</DasType>
      <DefaultExpression>0.00</DefaultExpression>
      <Position>4</Position>
    </column>
    <column id="1002" parent="355" name="tenant_id">
      <Comment>租户编号</Comment>
      <DasType>varchar(20)|0s</DasType>
      <DefaultExpression>&apos;000000&apos;</DefaultExpression>
      <Position>5</Position>
    </column>
    <column id="1003" parent="355" name="dept_id">
      <Comment>部门ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="1004" parent="355" name="user_name">
      <Comment>用户账号</Comment>
      <DasType>varchar(30)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="1005" parent="355" name="nick_name">
      <Comment>用户昵称</Comment>
      <DasType>varchar(30)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>8</Position>
    </column>
    <column id="1006" parent="355" name="user_type">
      <Comment>用户类型（sys_user系统用户）</Comment>
      <DasType>varchar(10)|0s</DasType>
      <DefaultExpression>&apos;sys_user&apos;</DefaultExpression>
      <Position>9</Position>
    </column>
    <column id="1007" parent="355" name="user_plan">
      <Comment>用户套餐</Comment>
      <DasType>varchar(255)|0s</DasType>
      <DefaultExpression>&apos;Free&apos;</DefaultExpression>
      <Position>10</Position>
    </column>
    <column id="1008" parent="355" name="email">
      <Comment>用户邮箱</Comment>
      <DasType>varchar(50)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>11</Position>
    </column>
    <column id="1009" parent="355" name="phonenumber">
      <Comment>手机号码</Comment>
      <DasType>varchar(11)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>12</Position>
    </column>
    <column id="1010" parent="355" name="sex">
      <Comment>用户性别（0男 1女 2未知）</Comment>
      <DasType>char(1)|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>13</Position>
    </column>
    <column id="1011" parent="355" name="avatar">
      <Comment>头像地址</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>14</Position>
    </column>
    <column id="1012" parent="355" name="wx_avatar">
      <Comment>微信头像地址</Comment>
      <DasType>varchar(500)|0s</DasType>
      <Position>15</Position>
    </column>
    <column id="1013" parent="355" name="password">
      <Comment>密码</Comment>
      <DasType>varchar(100)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>16</Position>
    </column>
    <column id="1014" parent="355" name="status">
      <Comment>帐号状态（0正常 1停用）</Comment>
      <DasType>char(1)|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>17</Position>
    </column>
    <column id="1015" parent="355" name="del_flag">
      <Comment>删除标志（0代表存在 2代表删除）</Comment>
      <DasType>char(1)|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>18</Position>
    </column>
    <column id="1016" parent="355" name="login_ip">
      <Comment>最后登录IP</Comment>
      <DasType>varchar(128)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>19</Position>
    </column>
    <column id="1017" parent="355" name="login_date">
      <Comment>最后登录时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>20</Position>
    </column>
    <column id="1018" parent="355" name="domain_name">
      <Comment>注册域名</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>21</Position>
    </column>
    <column id="1019" parent="355" name="create_dept">
      <Comment>创建部门</Comment>
      <DasType>bigint|0s</DasType>
      <Position>22</Position>
    </column>
    <column id="1020" parent="355" name="create_by">
      <Comment>创建者</Comment>
      <DasType>bigint|0s</DasType>
      <Position>23</Position>
    </column>
    <column id="1021" parent="355" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>24</Position>
    </column>
    <column id="1022" parent="355" name="update_by">
      <Comment>更新者</Comment>
      <DasType>bigint|0s</DasType>
      <Position>25</Position>
    </column>
    <column id="1023" parent="355" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>26</Position>
    </column>
    <column id="1024" parent="355" name="remark">
      <Comment>备注</Comment>
      <DasType>varchar(500)|0s</DasType>
      <Position>27</Position>
    </column>
    <index id="1025" parent="355" name="PRIMARY">
      <ColNames>user_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1026" parent="355" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1027" parent="356" name="user_id">
      <Comment>用户ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1028" parent="356" name="post_id">
      <Comment>岗位ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <index id="1029" parent="356" name="PRIMARY">
      <ColNames>user_id
post_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1030" parent="356" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1031" parent="357" name="user_id">
      <Comment>用户ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1032" parent="357" name="role_id">
      <Comment>角色ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <index id="1033" parent="357" name="PRIMARY">
      <ColNames>user_id
role_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1034" parent="357" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1035" parent="358" name="new_commernt">
      <DasType>int|0s</DasType>
      <Position>1</Position>
    </column>
    <column id="1036" parent="358" name="create_dept">
      <DasType>varchar(100)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="1037" parent="358" name="create_by">
      <DasType>varchar(100)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="1038" parent="358" name="create_time">
      <DasType>varchar(100)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="1039" parent="358" name="update_by">
      <DasType>varchar(100)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="1040" parent="358" name="update_time">
      <DasType>varchar(100)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="1041" parent="358" name="id">
      <DasType>bigint|0s</DasType>
      <Position>7</Position>
    </column>
  </database-model>
</dataSource>