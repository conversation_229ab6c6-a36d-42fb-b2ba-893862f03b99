<script setup lang="ts">
import { computed, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';
import { $t } from '@vben/locales';
import { cloneDeep } from '@vben/utils';

import { useVbenForm } from '#/adapter/form';
import { manageAdd, manageInfo, manageUpdate } from '#/api/noval/manage';

import { modalSchema } from './data';

const emit = defineEmits<{ reload: [] }>();

const isUpdate = ref(false);
const title = computed(() => {
  return isUpdate.value ? $t('pages.common.edit') : $t('pages.common.add');
});

const [BasicForm, formApi] = useVbenForm({
  commonConfig: {
    // 默认占一列，让表单项可以并排显示
    formItemClass: 'col-span-1',
    // 默认label宽度 px
    labelWidth: 80,
    // 通用配置项 会影响到所有表单项
    componentProps: {
      class: 'w-full',
    }
  },
  schema: modalSchema(),
  showDefaultActions: false,
  showCollapseButton: false,
  collapseTriggerResize: false,
  submitOnChange: false,
  submitOnEnter: false,
  wrapperClass: 'grid-cols-2',
});

const [BasicModal, modalApi] = useVbenModal({
  // 在这里更改宽度
  class: 'w-[550px]',
  fullscreenButton: false,
  // 点击遮罩是否关闭
  closeOnClickModal: false,
  onCancel: handleCancel,
  onConfirm: handleConfirm,
  onOpenChange: async (isOpen) => {
    if (!isOpen) {
      return null;
    }
    modalApi.modalLoading(true);

    const { id } = modalApi.getData() as { id?: number | string };
    isUpdate.value = !!id;

    if (isUpdate.value && id) {
      const record = await manageInfo(id);
      await formApi.setValues(record);
    }

    modalApi.modalLoading(false);
  },
});

async function handleConfirm() {
  try {
    modalApi.modalLoading(true);
    const { valid } = await formApi.validate();
    if (!valid) {
      return;
    }
    // getValues获取为一个readonly的对象 需要修改必须先深拷贝一次
    const data = cloneDeep(await formApi.getValues());

    if (isUpdate.value) {
      await manageUpdate(data);
    } else {
      // 新增操作，获取返回的数据（包含ID）
      const result = await manageAdd(data);
      // 如果后端返回了ID，将其设置到modalApi的数据中
      if (result && result.id) {
        data.id = result.id;
        modalApi.setData(data);
      }
    }

    emit('reload');
    await handleCancel();
  } catch (error) {
    console.error(error);
  } finally {
    modalApi.modalLoading(false);
  }
}

async function handleCancel() {
  modalApi.close();
  await formApi.resetForm();
}
</script>

<template>
  <BasicModal :title="title">
    <div class="form-container">
      <BasicForm />
    </div>
  </BasicModal>
</template>

<style scoped>
.form-container {
  max-height: 400px;
  overflow-y: auto;
  padding: 16px 0;
}

.form-container :deep(.grid) {
  display: grid !important;
  grid-template-columns: repeat(2, 1fr) !important;
  gap: 16px !important;
  grid-auto-rows: min-content !important;
  height: auto !important;
  min-height: auto !important;
}

.form-container :deep(.col-span-2) {
  grid-column: span 2 !important;
}

.form-container :deep(.col-span-1) {
  grid-column: span 1 !important;
}


</style>
