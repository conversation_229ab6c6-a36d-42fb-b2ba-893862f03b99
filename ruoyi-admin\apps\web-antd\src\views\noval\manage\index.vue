<script setup lang="ts">
import type { Recordable } from '@vben/types';

import { ref } from 'vue';

import { Page, useVbenModal, type VbenFormProps } from '@vben/common-ui';
import { getVxePopupContainer } from '@vben/utils';

import { Modal, Popconfirm, Space, Card, Button } from 'ant-design-vue';
import dayjs from 'dayjs';

import {
  useVbenVxeGrid,
  vxeCheckboxChecked,
  type VxeGridProps
} from '#/adapter/vxe-table';

import {
  manageExport,
  manageList,
  manageRemove,
} from '#/api/noval/manage';
import type { ManageForm } from '#/api/noval/manage/model';
import { commonDownloadExcel } from '#/utils/file/download';

import manageModal from './manage-modal.vue';
import { columns, querySchema } from './data';

import { VbenIcon } from '@vben/icons';

// 页面标题
defineOptions({
  name: 'NovelManage',
});

const formOptions: VbenFormProps = {
  commonConfig: {
    labelWidth: 100,
    componentProps: {
      allowClear: true,
      size: 'large',
    },
  },
  schema: querySchema(),
  wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6',
  actionWrapperClass: 'flex justify-end gap-3 pt-4',
  // 处理区间选择器RangePicker时间格式 将一个字段映射为两个字段 搜索/导出会用到
  // 不需要直接删除
  // fieldMappingTime: [
  //  [
  //    'createTime',
  //    ['params[beginTime]', 'params[endTime]'],
  //    ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59'],
  //  ],
  // ],
};

const gridOptions: VxeGridProps = {
  checkboxConfig: {
    // 高亮
    highlight: true,
    // 翻页时保留选中状态
    reserve: true,
    // 点击行选中
    // trigger: 'row',
  },
  // 需要使用i18n注意这里要改成getter形式 否则切换语言不会刷新
  // columns: columns(),
  columns,
  // 避免自适应高度导致表格反复增高，使用最大高度限制
  maxHeight: 600,
  keepSource: true,
  pagerConfig: {
    background: true,
    perfect: true,
    pageSize: 20,
    pageSizes: [10, 20, 50, 100],
    layouts: ['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'Total'],
  },
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues = {}) => {
        return await manageList({
          pageNum: page.currentPage,
          pageSize: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  rowConfig: {
    keyField: 'id',
    isHover: true,
    useKey: true,
  },
  // 表格全局唯一表示 保存列配置需要用到
  id: 'noval-manage-index',
  border: true,
  stripe: true,
  round: true,
};

const [BasicTable, tableApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

const [ManageModal, modalApi] = useVbenModal({
  connectedComponent: manageModal,
});

function handleAdd() {
  modalApi.setData({});
  modalApi.open();
}

async function handleEdit(row: Required<ManageForm>) {
  modalApi.setData({ id: row.id });
  modalApi.open();
}

async function handleDelete(row: Required<ManageForm>) {
  await manageRemove(row.id);
  await tableApi.query();
}

function handleMultiDelete() {
  const rows = tableApi.grid.getCheckboxRecords();
  const ids = rows.map((row: Required<ManageForm>) => row.id);
  Modal.confirm({
    title: '提示',
    okType: 'danger',
    content: `确认删除选中的${ids.length}条记录吗？`,
    onOk: async () => {
      await manageRemove(ids);
      await tableApi.query();
    },
  });
}

function handleDownloadExcel() {
  commonDownloadExcel(manageExport, '书库管理数据', tableApi.formApi.form.values, {
    fieldMappingTime: formOptions.fieldMappingTime,
  });
}
</script>

<template>
  <Page
    :auto-content-height="true"
    content-class="p-6 bg-gray-50/50"
  >
    <Card
      class="shadow-lg border-0 rounded-xl overflow-hidden"
      :bordered="false"
    >
      <template #title>
        <div class="flex items-center gap-3">
          <div class="w-1 h-6 bg-primary rounded-full"></div>
          <span class="text-xl font-semibold text-gray-900 dark:text-white flex items-center gap-2"><VbenIcon icon="ep:collection" class="text-gray-900 dark:text-white" /> 书库管理中心</span>
        </div>
      </template>
      <template #extra>
        <div class="text-sm text-gray-500">
          管理您的小说书库信息
        </div>
      </template>

      <BasicTable
        table-title=""
        class="modern-table"
      >
        <template #toolbar-tools>
          <Space class="gap-3">
            <Button
              v-access:code="['noval:manage:export']"
              @click="handleDownloadExcel"
              class="flex items-center gap-2"
            >
              <VbenIcon icon="ep:download" class="text-base" />
              {{ $t('pages.common.export') }}
            </Button>
            <Button
              :disabled="!vxeCheckboxChecked(tableApi)"
              type="default"
              v-access:code="['noval:manage:remove']"
              @click="handleMultiDelete"
              class="flex items-center gap-2"
            >
              <VbenIcon icon="ep:delete" class="text-base" />
              {{ $t('pages.common.delete') }}
            </Button>
            <Button
              type="primary"
              v-access:code="['noval:manage:add']"
              @click="handleAdd"
              class="flex items-center gap-2 px-6"
            >
              <VbenIcon icon="ep:plus" class="text-base text-white" />
              {{ $t('pages.common.add') }}
            </Button>
          </Space>
        </template>
        <template #action="{ row }">
          <Space class="gap-2">
            <Button
              v-access:code="['noval:manage:edit']"
              @click.stop="handleEdit(row)"
              type="primary"
              size="small"
              class="flex items-center gap-1"
            >
              <VbenIcon icon="ep:edit" class="text-sm" />
              {{ $t('pages.common.edit') }}
            </Button>
            <Popconfirm
              :get-popup-container="getVxePopupContainer"
              placement="left"
              title="确认删除这条记录吗？"
              ok-text="确认"
              cancel-text="取消"
              @confirm="handleDelete(row)"
            >
              <Button
                type="primary"
                danger
                v-access:code="['noval:manage:remove']"
                @click.stop=""
                size="small"
                class="flex items-center gap-1"
              >
                <VbenIcon icon="ep:delete" class="text-sm" />
                {{ $t('pages.common.delete') }}
              </Button>
            </Popconfirm>
          </Space>
        </template>
      </BasicTable>
    </Card>
    <ManageModal @reload="tableApi.query()" />
  </Page>
</template>

<style scoped>
.modern-table {
  @apply rounded-lg overflow-hidden;
}

.modern-table :deep(.vxe-table) {
  @apply border-0 rounded-lg;
}

.modern-table :deep(.vxe-table--header) {
  @apply bg-gray-100;
}

.modern-table :deep(.vxe-header--column) {
  @apply font-semibold text-gray-700 border-gray-200;
}

.modern-table :deep(.vxe-body--row:hover) {
  @apply bg-gray-100;
}

.modern-table :deep(.vxe-body--column) {
  @apply border-gray-100;
}

.modern-table :deep(.vxe-pager) {
  @apply bg-gray-50 text-gray-800 border-t border-gray-200 rounded-b-lg;
}
.modern-table :deep(.vxe-pager .is--active),
.modern-table :deep(.vxe-pager .vxe-pager--num-btn.is--active) {
  @apply bg-gray-200 text-primary border border-gray-300 rounded-md;
}


.modern-table :deep(.vxe-pager--wrapper) {
  @apply px-6 py-4;
}

.modern-table :deep(.vxe-button) {
  @apply rounded-md transition-all duration-200;
}

.modern-table :deep(.vxe-button--primary) {
  @apply bg-primary hover:bg-primary/90 border-transparent;
}

/* 统一浅色表单样式（仅作用于本页 Card） */
:deep(.ant-form-item-label > label) {
  color: #374151; /* text-gray-700 */
}
:deep(.ant-input,
  .ant-input-affix-wrapper,
  .ant-select-selector,
  .ant-picker,
  .ant-input-number,
  .ant-cascader-picker) {
  background-color: #ffffff;
  color: #111827; /* text-gray-900 */
  border-color: #E5E7EB; /* border-gray-200 */
}
:deep(.ant-input::placeholder) {
  color: #9CA3AF; /* text-gray-400 */
}
:deep(.ant-input:focus,
  .ant-input-affix-wrapper-focused,
  .ant-select-focused .ant-select-selector,
  .ant-picker-focused,
  .ant-input-number-focused,
  .ant-cascader-focused .ant-cascader-picker) {
  box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.15); /* primary #2563EB 15% */
  border-color: #93C5FD !important; /* lighter primary border */
}

</style>
